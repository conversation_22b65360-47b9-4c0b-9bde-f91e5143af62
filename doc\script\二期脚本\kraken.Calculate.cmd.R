###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-04-07 16:05:01
#' @FilePath: /liliuyang/R/Rscript.MASH/20240407/kraken.Calculate.cmd.R
#' @Description:  新增跳过beta_多样性计算的参数
###

suppressMessages(library(GetoptLong))
suppressMessages(library(reshape2))
suppressMessages(library(vegan))
suppressMessages(library(tidyverse))
suppressMessages(library(ape))
# setwd("E:/deepsea/R/metadata.20221101/R.5group") "s"表示字符串，"i"表示整数，"f"表示浮点数，"b"表示布尔值
rm(list = ls(all.names = TRUE))

RelaAbun_cal = "True"
Beta_cal = "True"
GetoptLong(
  "Source=s", "Total abundance table, e.g., kraken2_taxonomic_profiles.tsv",
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Abundance=s", "output Dir for Total Relative Abundance Table, e.g., Abundance_Table",
  "Barplot=s", "output Dir for Barplot Table, e.g., Barplot_Table",
  "Heatmap=s", "output Dir for Heatmap Table, e.g., Heatmap_Table",
  "PCoA=s", "output Dir for PCoA Table, e.g., PCoA_Table",
  "RelaAbun_cal=s", "option true or flase to determine whether to calculate relative abundance (influence Abundance calculate and Barplot analyses), default: True",
  "Beta_cal=s", "option true or flase to determine whether to calculate beta diversity (influence Heatmap and PCoA analyses), default: True",
  "verbose!","print messages"
)

#  "Beta_level=s", "option P C O F G S or A, represent ... species or ASV level, higher levels are also allowed",
##############add_rows_if_not_exists##############
add_rows_if_not_exists <- function(module_abundance, add_rows = c("K14126","K14128","K14127")) {
  for (row_name in add_rows) {
    if (!(row_name %in% rownames(module_abundance))) {
      module_abundance[row_name, ] <- 0
    }
  }
  return(module_abundance)
}
##############add_cols_if_not_exists##############
add_cols_if_not_exists <- function(module_abundance, add_cols = c("K14126","K14128","K14127")) {
  for (col_name in add_cols) {
    if (!(col_name %in% colnames(module_abundance))) {
      module_abundance[, col_name] <- 0
    }
  }
  return(module_abundance)
}
##########################      底层数据自定义计算函数     ##########################
extract_table = function(data = taxonomic_profiles,
                         domain_level = 'B',
                         tax_level = 'P'){
  #  从总的[物种row-样品col]表格,获取各个分类水平的[样品row-物种col]的相对丰度表格
  # 取分类的子集
  # domain_level can be any of the 'A', 'B', 'E', 'V'
  # tax_level can be any of the 'P', 'C', 'O', 'F', 'G', 'S'
  tax_df = data %>%
    {.[.$Domain %in% domain_level,, drop = F]} %>%
    {.[.$Level %in% tax_level,, drop = F]}
  rownames(tax_df) = tax_df$Clade_name
  # 保留数值列, 计算相对丰度
  tax_df.value = tax_df %>%
    {subset(., select=-c(get('Domain')))} %>%
    {subset(., select=-c(get('Level')))} %>%
    {subset(., select=-c(get('Clade_name')))}
  return(tax_df.value)
}

ra_cal = function(data = tax_df.value){
  #  从总的[物种row-样品col]表格,获取各个分类水平的[样品row-物种col]的相对丰度表格
  # data should be the species-sample table, and this table will be transferred to be a sample-species table with relative abundance
  tax_df.t = data %>%
    {.[rowSums(.)>0,, drop = F]} %>%
    {.[,colSums(.)>0, drop = F]} %>%
    {as.data.frame(t(.))} %>%
    {./rowSums(.)}
  return(tax_df.t)
}


tax_cal = function(data = tax_df.t,
                   tax_num = 29,
                   digit_num = 6){
  # 获取主要类群
  # data, 序列丰度表格
  # tax_num, 保留最丰富类群数量
  # digit_num, 相对丰度小数位数
  if (dim(data)[2] < tax_num) {
    need_data = data %>%
      {.[, order(colSums(.),decreasing = T), drop = F]} %>%
      {.[,1:dim(data)[2], drop = F]} #显示所有
  }
  else {
    need_data = data %>%
      {.[, order(colSums(.),decreasing = T), drop = F]} %>%
      {.[,1:tax_num, drop = F]} #前29丰度
  }
  Others = 1-rowSums(need_data)
  need_data = cbind(need_data, Others) %>%
    {round(.*100, digit_num)}
  return(need_data)

}

merge_cal = function(data = need_data,
                     group = group
){
  # 以'sample_name'列合并丰度表格和分组信息,并将分组转为因子向量
  # data 丰度表格
  # group 分组信息
  data$sample_name <- rownames(data)
  data_merge = merge(data, group, by='sample_name', all.x = TRUE)
  # name for the sample
  rownames(data_merge)=data_merge$sample_name
  # 按照分组顺序排序
  data_merge_order = data_merge[order(data_merge$group, decreasing = F), ]
  # 赋予分组因子
  levels_group = sort(unique(group$group))
  data_merge_order$group <- factor(data_merge_order$group, levels = levels_group)
  return(data_merge_order)
}


melt_cal = function(data = need_data,
                    group = group,
                    variable.name='Taxonomy',
                    value.name='Abudance'
){
  # wide to long, 以'sample_name'列合并丰度表格和分组信息, 并将数据转为长数据格式
  # data 丰度表格
  # group 分组信息
  colname_data = colnames(data)
  plotdata_group = merge_cal(data, group) %>%
    melt(.,id.vars=c('sample_name','group'),measure.vars=colname_data,
         variable.name=variable.name,
         value.name=value.name)
  return(plotdata_group)
}

generate_tables_and_plots <- function(ra_tax, sub_taxonomic_profiles, domain, taxonomy, Abundance, Barplot, group) {
  if (dim(ra_tax)[1] >= 1) {
    # output the 24 tables
    ra_tax %>%
      {round(.*100, 6)} %>%
      {as.data.frame(t(.))} %>%
      {add_rows_if_not_exists(., add_rows = rownames(sub_taxonomic_profiles))} %>%
      {add_cols_if_not_exists(., add_cols = colnames(sub_taxonomic_profiles))} %>%
      {rownames_to_column(., var='taxon')} %>%
      {write.table(.,
                   paste0(Abundance, "/", domain, ".", taxonomy, ".percent.csv"),
                   sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}

    barplot_table = ra_tax %>%
      {tax_cal(., 29, 4)} %>%
      {add_rows_if_not_exists(., add_rows = colnames(sub_taxonomic_profiles))}
    barlegend = barplot_table %>%
      {data.frame(Tax = colnames(.))}
    barplot_table = barplot_table %>%
      {melt_cal(., group, 'Taxonomy', 'Abudance')}
  } else {
    barplot_table = data.frame(
      sample_name = group$sample_name,
      group = group$group,
      Taxonomy = "",
      Abudance = 0
    )
    #print(barplot_table)
    barlegend = data.frame(Tax = "")
    #print(barlegend)
  }
  barplot_table %>% {write.table(.,
                                 paste0(Barplot, "/", "Barplot.", domain, ".", taxonomy, ".csv"),
                                 sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
  barlegend %>%
    {write.table(.,
                 paste0(Barplot,"/", "Barlegend.", domain, ".", taxonomy, ".csv"),
                 sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
}

generate_heatmap_and_pcoa <- function(ra_tax, domain, Beta_level, beta, Heatmap, PCoA, group) {
  if (dim(ra_tax)[1] >= 1) {
    tryCatch({
      dist_bray = ra_tax %>%
        vegdist(., method = beta, na.rm = TRUE) %>%
        as.matrix(as.dist(.))

      if (dim(dist_bray)[1] >= 3) {
        dist_bray %>%
          {round(., 6)} %>%
          {as.data.frame(.)} %>%
          {rownames_to_column(., var='sample_name')} %>%
          {write.table(.,
                       paste0(Heatmap, "/", "Heatmap.", domain, ".", Beta_level, ".", beta, ".csv"),
                       sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}

        print(dist_bray)
        tryCatch({
          df.pcoa = pcoa(dist_bray, correction = "cailliez")
          data.frame(
            x_label = round(df.pcoa$values$Rel_corr_eig[1]*100,1),
            y_label = round(df.pcoa$values$Rel_corr_eig[2]*100,1)
          ) %>%
            {write.table(.,
                          paste0(PCoA, "/", "PCoA.label.", domain, ".", Beta_level, ".", beta, ".csv"),
                          sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
          df.pcoa %>%
            {data.frame(.$vectors)} %>%
            {merge_cal(., group)} %>%
            {.[colnames(.) %in% c('sample_name','group','Axis.1','Axis.2')]} %>%
            {write.table(.,
                          paste0(PCoA, "/", "PCoA.", domain, ".", Beta_level, ".", beta, ".csv"),
                          sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
        }, error = function(calculate.pcoa) {
          print(paste("Caught an error for calculate.pcoa:", calculate.pcoa))
        })

      }
    }, error = function(calculate.dist_bray) {
      print(paste("Caught an error for calculate.dist_bray:", calculate.dist_bray))
    })
  }
}

##########################      Data input      ##########################
if ((RelaAbun_cal %in% c("TRUE","True","true","T","t")) || (Beta_cal %in% c("TRUE","True","true","T","t"))){
  print(paste0("Loading taxonomic profile table: ", Source))
  taxonomic_profiles <- read.csv(Source, sep = '\t', quote='',check.names = F)
  print(paste0("Loading sample group table: ", Group))
  group = read.csv(Group,sep='\t',header = TRUE,row.names = 1,check.names = F)
  group$sample_name <- rownames(group) #添加sample_name列

  ################################## for循环跑每个类别 ##################################
  for (variable in c(Abundance,Barplot,Heatmap,PCoA)) {
    if(! dir.exists(variable)){
      dir.create(variable)
    }
  }
  domain_range = unique(taxonomic_profiles$Domain)[unique(taxonomic_profiles$Domain) %in% c('A', 'E', 'V', 'B')]
  if (RelaAbun_cal %in% c("TRUE","True","true","T","t")){

    for (domain in domain_range) {
      print(paste0('Domain: ',domain))
      for (taxonomy in c('P', 'C', 'O', 'F', 'G', 'S')) {
        print(paste0('Taxonomy: ',taxonomy))
        sub_taxonomic_profiles = extract_table(taxonomic_profiles, domain, taxonomy)
        ra_tax = ra_cal(sub_taxonomic_profiles)
        generate_tables_and_plots(ra_tax, sub_taxonomic_profiles, domain, taxonomy, Abundance, Barplot, group)
      }
    }
  } else if (RelaAbun_cal %in% c("FALSE","False","false","F","f")) {
    print("--RelaAbun_cal is False, kraken.Calculate.cmd.R will skip the relative abundance calculate!")
  } else {
    print(paste0("Unknown parameter for --RelaAbun_cal: ", RelaAbun_cal))
  }

  if (Beta_cal %in% c("TRUE","True","true","T","t")){
    print("Calculating beta diversity!")
    for (domain in domain_range) {
      print(paste0('Domain: ',domain))
      for (Beta_level in c('S','G','F','O','C','P')) {
        print(paste0('Beta_level: ',Beta_level))
        for (beta in c('bray', 'jaccard')) {
          print(paste0('Method: ', beta))

          sub_taxonomic_profiles = extract_table(taxonomic_profiles, domain, Beta_level)
          ra_tax = ra_cal(sub_taxonomic_profiles)
          generate_heatmap_and_pcoa(ra_tax, domain, Beta_level, beta, Heatmap, PCoA, group)
        }
      }
    }
  } else if (Beta_cal %in% c("FALSE","False","false","F","f")) {
    print("--Beta_cal is False, kraken.Calculate.cmd.R will skip the beta diversity calculate!")
  } else {
    print(paste0("Unknown parameter for --Beta_cal: ", Beta_cal))
  }
} else if (RelaAbun_cal %in% c("FALSE","False","false","F","f")) {
  print("--RelaAbun_cal is False, kraken.Calculate.cmd.R will skip the relative abundance calculate!")
  if (Beta_cal %in% c("FALSE","False","false","F","f")) {
    print("--Beta_cal is False, kraken.Calculate.cmd.R will skip the beta diversity calculate!")
  } else {
    print(paste0("Unknown parameter for --Beta_cal: ", Beta_cal))
  }
} else if (Beta_cal %in% c("FALSE","False","false","F","f")) {
  print(paste0("Unknown parameter for --RelaAbun_cal: ", RelaAbun_cal))
  print("--Beta_cal is False, kraken.Calculate.cmd.R will skip the beta diversity calculate!")
} else {
  print(paste0("Unknown parameter for --RelaAbun_cal: ", RelaAbun_cal))
  print(paste0("Unknown parameter for --Beta_cal: ", Beta_cal))
}
