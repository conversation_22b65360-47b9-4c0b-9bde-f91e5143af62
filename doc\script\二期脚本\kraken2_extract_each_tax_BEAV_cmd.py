#! python3
# -*- coding: utf-8 -*-
"""
Created on %(date)s

@author: <PERSON>.Gladius.Hu Corvi %(username)s
"""
""" Descripction of Program """
""" 提取kraken2的结果，会把每一个样品的每一个分类阶原都生成一个文件"""

import re, os, glob, sys
import pandas as pd

# 存放bracken_speceis_report的路径
bracken_reports_folder_path = sys.argv[1]
# 矩阵的输出路径(不是文件名)
output_matrix_path = sys.argv[2]
try:
    #
    search_sample_suffix_name = sys.argv[3]
except IndexError:
    # 如果bracken_species.report的文件名是固定的，也可以修改第20行成固定要用的格式。可以只输入2个参数
    search_sample_suffix_name = ".k2_pluspf_16gb_20220908_bracken_species.report"

re_num = re.compile("[0-9]")
# 默认输出的文件名为kraken2_taxonomic_profiles.tsv,有需要可以更改
result_file = os.path.join(output_matrix_path, "kraken2_taxonomic_profiles.tsv")
df_1_verify = 0
# linux系统
bracken_files_path = bracken_reports_folder_path + "/*" + search_sample_suffix_name
# windows系统
# bracken_files_path = bracken_reports_folder_path + "\*" + search_sample_suffix_name
bracken_files_list = glob.glob(bracken_files_path)
for file_path in bracken_files_list:
    # linux系统
    sample_name = file_path.replace(bracken_reports_folder_path, "").replace("/", "")
    # windows系统
    # sample_name = file_path.replace(bracken_reports_folder_path, "").replace("\\", "")
    sample_name = sample_name.replace(search_sample_suffix_name, "")
    # 初始化
    tax_dict = {}
    tax_big_pointer = ""
    abs_file_path = os.path.join(bracken_reports_folder_path, file_path)
    try:
        file_var = open(abs_file_path, "r")
        lines = file_var.readlines()
        file_var.close()
        jump_first_line_var = 0
        index = 0
        for line in lines:
            # 跳过 Root, unclassified
            # if jump_first_line_var <= 1:
            #     jump_first_line_var += 1
            #     continue
            # 筛选掉含有数字的tax_marker(亚纲，亚门 等)
            tax_marker = line.split("\t")[3]
            try:
                re_tax_marker_search = re_num.search(tax_marker)
                re_tax_marker_result = re_tax_marker_search.group()
                continue
            except AttributeError:
                pass
            tax_name = line.split("\t")[5].replace("\n", "").lstrip(" ")
            try:
                # 0为丰度，1为reads数量
                abundance = line.split("\t")[1]
                # 物种指针确认
                if (tax_marker == "D") and (tax_name == "Bacteria"):
                    tax_big_pointer = "B"
                elif (tax_marker == "D") and (tax_name == "Eukaryota"):
                    tax_big_pointer = "E"
                elif (tax_marker == "D") and (tax_name == "Archaea"):
                    tax_big_pointer = "A"
                elif (tax_marker == "D") and (tax_name == "Viruses"):
                    tax_big_pointer = "V"
                elif (tax_marker == "R") and (tax_name == "root"):
                    continue
                # 用于kraken_report文件,合并bracken_species_report不需要使用
                # elif (tax_marker == "U") and (tax_name == "unclassified"):
                #     tax_big_pointer = "U"
                ###############################################
                # 根据情况更新字典
                temp_dict = {index: [tax_name, tax_marker, tax_big_pointer, abundance]}
                tax_dict.update(temp_dict)
                index += 1
            except AttributeError:
                pass
    except FileNotFoundError:
        pass
    if df_1_verify == 1:
        # df_1存在，执行拼接
        df_2 = pd.DataFrame.from_dict(tax_dict, orient="index", columns=["Clade_name", "Level", "Domain", sample_name])
        df_1 = pd.merge(df_1, df_2, how="outer", on=["Clade_name", "Level", "Domain"])
        # print("df_1", df_1)
    elif df_1_verify == 0:
        # df_1不存在，创建df_1
        df_1 = pd.DataFrame.from_dict(tax_dict, orient="index", columns=["Clade_name", "Level", "Domain", sample_name])
        df_1_verify = 1
df_1 = df_1.fillna(0)
df_1.to_csv(result_file, index=False, sep="\t")

if __name__ == '__main__':
    print("Run Self")

