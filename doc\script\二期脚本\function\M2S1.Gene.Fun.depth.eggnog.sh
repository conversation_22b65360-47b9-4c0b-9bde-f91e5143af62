#!/bin/bash
:<<!EOF!
 * @Date: 2023-06-14 16:58:11
 * @LastEditors: LiuyangL<PERSON>
 * @LastEditTime: 2023-08-17 21:16:52
 * @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M2S1.Gene.Fun.depth.eggnog.sh
 * @Description: 每个批次的prefix要唯一, 新增了输出 KO 对应丰度的功能
!EOF!

help_message () {
	echo ""
	echo "Usage: M2S1.Gene.Fun.depth.eggnog.sh [options] -u user_input -l sample_list -m AAA_Sample_KO_depth -d Sample_KO_Sample.long.tsv -s script_dir -k KO_list.tsv -p 20230812 -o output_dir"
	echo "Note1: Make sure to provide directory and file prefix."
	echo ""
	echo "Options:"
	echo ""
	echo "	-u STR          file for gene annotation or KO ID, e.g., K00032, depending on the switch of --KO_ID and -gene"
	echo "	-l STR          file for MASH sample list"
	echo "	-m STR          file for the sum of all single MASH_Sample_KO_depth"
	echo "	-d STR          directory for each single MASH_Sample_KO_depth"
	echo "	-s STR          directory for script"
	echo "	-k STR          file for KO_list"
	echo "	-p STR          prefix for each sample list, should be unique for each batch"
	echo "	-o STR          directory for output and error files (if exist). e.g., prefix.Sample_KO.tsv, prefix.error_KO.tsv"
	echo "	--KO_ID         user input a KO ID to search MASH database"
	echo "	--gene          user input gene eggnog annotation to search MASH database"
	echo "";}

########################################################################################################
########################               LOADING IN THE PARAMETERS                ########################
########################################################################################################
#
# long options defaults
KO_ID=false; gene=false;

## load in params
OPTS=`getopt -o hu:l:m:d:s:k:p:o: --long user_input:,list_sample:,MASH_Sample_KO_depth_DIR:,db_MASH_Sample_KO_depth:,script_DIR:,KO_list:,prefix:,output_DIR:,KO_ID,gene,help -- "$@"`
if [ $? -ne 0 ]; then help_message; exit 1; fi
# loop through input params
while true; do
		case "$1" in
				-u) user_input=$2; shift 2;;
				-l) list_sample=$2; shift 2;;
				-m) MASH_Sample_KO_depth_DIR=$2; shift 2;;
				-d) db_MASH_Sample_KO_depth=$2; shift 2;;
				-s) script_DIR=$2; shift 2;;
				-k) KO_list=$2; shift 2;;
				-p) prefix=$2; shift 2;;
				-o) output_DIR=$2; shift 2;;
				--KO_ID) KO_ID=true; shift 1;;
				--gene) gene=true; shift 1;;
				-h | --help) help_message; exit 1; shift 1;;
				--) help_message; exit 1; shift; break;;
				*) break;;
		esac
done

########################################################################################################
########################           MAKING SURE EVERYTHING IS SET UP             ########################
########################################################################################################

# Make sure one of the two method was chosen
if [ $KO_ID = false ] && [ $gene = false ]; then
	echo "You must select one method: --KO_ID or --gene"
	help_message; exit 1
fi

#check if user emapper annotation file exists
if [ -z ${user_input} ]; then
	echo "Please input user emapper annotation file."
	help_message; exit 1
else
	if [ ! -s $user_input ]; then
		echo "$user_input does not exist. Exiting..."
		help_message; exit 1
	fi
fi

#check if the MASH sample list file exists
if [ -z ${list_sample} ]; then
	echo "Please input MASH sample list file."
	help_message; exit 1
else
	if [ ! -s $list_sample ]; then
		echo "The file $list_sample does not exist. Exiting..."
		help_message; exit 1
	fi
fi

#check if the MASH emapper annotation directory exists
if [ -z ${MASH_Sample_KO_depth_DIR} ]; then
	echo "Please input the MASH emapper annotation directory."
	help_message; exit 1
else
	if [ ! -d $MASH_Sample_KO_depth_DIR ]; then
		echo "The directory $MASH_Sample_KO_depth_DIR does not exist. Exiting..."
		help_message; exit 1
	fi
fi


#check if the MASH emapper annotation file exists
if [ -z ${db_MASH_Sample_KO_depth} ]; then
	echo "Please input the merged MASH emapper annotation file."
	help_message; exit 1
	if [ ! -s $db_MASH_Sample_KO_depth ]; then
		echo "The file $db_MASH_Sample_KO_depth does not exist. Exiting..."
		help_message; exit 1
	fi
fi

# Check for scripts folder
if [ -z ${script_DIR} ]; then
	echo "Please input the scripts directory."
	help_message; exit 1
else
	if [ ! -s ${script_DIR}/M2S1.Sample_KO_depth.cmd.R ]; then
		echo "The file ${script_DIR}/M2S1.Sample_KO_depth.cmd.R does not exist."
		help_message; exit 1
	fi

	if [ ! -s ${script_DIR}/M2S1.Sample_KO_ID_depth.cmd.R ]; then
		echo "The file ${script_DIR}/M2S1.Sample_KO_ID_depth.cmd.R does not exist."
		help_message; exit 1
	fi
fi


# Check for KO_list.tsv
if [ -z ${KO_list} ]; then
	echo "Please input KO_list.tsv file."
	help_message; exit 1
else
	if [ ! -s ${KO_list} ]; then
		echo "The file ${KO_list} does not exist."
		help_message; exit 1
	fi
fi

#check if the prefix exists
if [ -z $prefix ]; then
	echo "Please input the prefix. Exiting..."
	help_message; exit 1
fi


#check if the output_DIR dir exists
if [ -z ${output_DIR} ]; then
	echo "Please input the output_DIR."
	help_message; exit 1
fi
########################################################################################################
########################                    BEGIN PIPELINE!                     ########################
########################################################################################################



output_DIR_user_Sample_orf_per_KO=${output_DIR}/user_Sample_orf_per_KO/
output_DIR_MASH_Sample_per_KO=${output_DIR}/MASH_Sample_per_KO/
output_DIR_MASH_Sample_per_KO=${output_DIR}/MASH_Sample_per_KO/
output_DIR_user_error=${output_DIR}/user_error/
output_DIR_MASH_error=${output_DIR}/MASH_error/
output_DIR_tmp_MASH_KO_Sample_long=${output_DIR}/tmp_MASH_KO_Sample_long/
output_DIR_user_gene_annotations=${output_DIR}/user_gene_annotations/

user_error=${output_DIR_user_error}/${prefix}.error.tsv
MASH_error=${output_DIR_MASH_error}/${prefix}.error.tsv
tmp_MASH_KO_Sample_long=${output_DIR_tmp_MASH_KO_Sample_long}/${prefix}.KO_depth.tsv
user_gene_annotations=${output_DIR_user_gene_annotations}/${prefix}.gene.emapper.annotations.scale
input_user_gene_orf_KO=${output_DIR_user_gene_annotations}/${prefix}.orf_KO.tsv

if [ ! -d ${output_DIR} ]; then mkdir -p ${output_DIR};
else
	echo "Warning: ${output_DIR} already exists."
fi


if [ ! -d ${output_DIR_user_Sample_orf_per_KO}/ ]; then mkdir -p ${output_DIR_user_Sample_orf_per_KO}/;
else
	echo "Warning: ${output_DIR_user_Sample_orf_per_KO}/ already exists."
	rm -r ${output_DIR_user_Sample_orf_per_KO}/${prefix}.user_Sample_orf.*.tsv
fi


if [ ! -d ${output_DIR_MASH_Sample_per_KO}/ ]; then mkdir -p ${output_DIR_MASH_Sample_per_KO}/;
else
	echo "Warning: ${output_DIR_MASH_Sample_per_KO}/ already exists."
	rm -r ${output_DIR_MASH_Sample_per_KO}/${prefix}.MASH_Sample.*.tsv
fi

if [ ! -d ${output_DIR_user_error}/ ]; then mkdir -p ${output_DIR_user_error}/;
else
	echo "Warning: ${output_DIR_user_error} already exists."
	if [ -f ${user_error} ]; then rm -r ${user_error}; fi
fi

if [ ! -d ${output_DIR_MASH_error}/ ]; then mkdir -p ${output_DIR_MASH_error}/;
else
	echo "Warning: ${output_DIR_MASH_error}/ already exists."
	if [ -f ${MASH_error} ]; then rm -r ${MASH_error}; fi
fi

if [ ! -d ${output_DIR_tmp_MASH_KO_Sample_long}/ ]; then mkdir -p ${output_DIR_tmp_MASH_KO_Sample_long}/;
else
	echo "Warning: ${output_DIR_tmp_MASH_KO_Sample_long}/ already exists."
	if [ -f ${tmp_MASH_KO_Sample_long} ]; then rm -r ${tmp_MASH_KO_Sample_long}; fi
fi

if [ ! -d ${output_DIR_user_gene_annotations}/ ]; then mkdir -p ${output_DIR_user_gene_annotations}/;
else
	echo "Warning: ${output_DIR_user_gene_annotations}/ already exists."
	if [ -f ${user_gene_annotations} ]; then rm -r ${user_gene_annotations}; fi
	if [ -f ${input_user_gene_orf_KO} ]; then rm -r ${input_user_gene_orf_KO}; fi
fi
########################################################################################################
########################            Make database for gene extract              ########################
########################################################################################################

Line0=`wc -l ${list_sample}| awk '{print $1}'`
if [ $Line0 -gt 5 ];
then
	# 用这种方式，每次用户提交任务要读入一个合并了所有数据的大文件
	echo "样品数量大于50个, 直接导入所有数据..."
	input_MASH_Sample_KO_depth=${db_MASH_Sample_KO_depth}
else
	# 用这种方式，每次用户提交任务都得从头合并一遍
	echo "样品数量小于等于50个, 临时合并数据..."
	for sample in `cat ${list_sample}`
	do
			if [ ! -s ${MASH_Sample_KO_depth_DIR}/${sample}.KO_depth.tsv ]; then
					echo "${MASH_Sample_KO_depth_DIR}/${sample}.KO_depth.tsv does not exist. Skiping ${sample}..."
			else
					cat ${MASH_Sample_KO_depth_DIR}/${sample}.KO_depth.tsv >> ${tmp_MASH_KO_Sample_long}
			fi
	done
	input_MASH_Sample_KO_depth=${tmp_MASH_KO_Sample_long}
fi


########################################################################################################
########################                   RUNNING gene                     ########################
########################################################################################################
if [ $gene = true ]; then
	# 如果输入基因文件的注释结果
	awk -F '\t' 'BEGIN {OFS="\t"} {print $1,$12}' ${user_input} > ${user_gene_annotations}
	num=3 # 要删除的行数
	max=`sed -n '$=' ${user_gene_annotations}` # 文件总行数
	let sLine=max-num+1   # 删除的起始行
	sed $sLine',$d' ${user_gene_annotations} | sed '1,5d' > ${input_user_gene_orf_KO} #从起始行删除到最后行,再删除前5行
	echo "Running ${script_DIR}/M2S1.Sample_KO_depth.cmd.R !!!"
	Rscript ${script_DIR}/M2S1.Sample_KO_depth.cmd.R \
			--in_user_gene_orf_KO ${input_user_gene_orf_KO} \
			--in_MASH_Sample_KO_depth ${input_MASH_Sample_KO_depth} \
			--out_DIR_user_Sample_orf_per_KO ${output_DIR_user_Sample_orf_per_KO} \
			--out_DIR_MASH_Sample_per_KO ${output_DIR_MASH_Sample_per_KO} \
			--out_DIR_user_error ${output_DIR_user_error} \
			--out_DIR_MASH_error ${output_DIR_MASH_error} \
			--prefix ${prefix}
fi

########################################################################################################
########################                   RUNNING KO_ID                     ########################
########################################################################################################
if [ $KO_ID = true ]; then
	# 如果输入KO ID,判断该 KO 是否是 KO_list 中的一个。如果不是，给出错误提示。
	KO_present_in_list=no
	for userKO in `cat ${user_input}| awk '{print $1}'`
	do
		echo "Running ${userKO}!!!"
		for line in `cat ${KO_list}| awk '{print $1}'`
		do
			if [[ "$userKO" == "$line" ]]; then KO_present_in_list=yes; fi
		done

		if [[ "$KO_present_in_list" == "no" ]]; then
			touch ${user_error}
		else
			echo "$userKO is present in the KO list!!!"
			echo "Running ${script_DIR}/M2S1.Sample_KO_ID_depth.cmd.R !!!"
			Rscript ${script_DIR}/M2S1.Sample_KO_ID_depth.cmd.R \
					--in_user_KO ${user_input} \
					--in_MASH_Sample_KO_depth ${input_MASH_Sample_KO_depth} \
					--out_DIR_MASH_Sample_per_KO ${output_DIR_MASH_Sample_per_KO} \
					--out_DIR_MASH_error ${output_DIR_MASH_error} \
					--prefix ${prefix}
		fi
	done
fi

########################################################################################################
########################            M2S1 SUCCESSFULLY FINISHED!!!               ########################
########################################################################################################
echo "M2S1 PIPELINE SUCCESSFULLY FINISHED!!!"
