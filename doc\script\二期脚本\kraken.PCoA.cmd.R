###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-04-07 11:28:17
#' @FilePath: /liliuyang/R/Rscript.MASH/20240407/kraken.PCoA.cmd.R
#' @Description:

suppressMessages(library(GetoptLong))
suppressMessages(library(ggplot2))
suppressMessages(library(tidyverse))
suppressMessages(library(htmlwidgets))
suppressMessages(library(plotly))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Group_color=s", "group table, e.g., group_color.txt with two coloumn: Group and Color",
  "Input=s", "input Dir for PCoA Table, e.g., PCoA_Table",
  "Output=s", "output Dir for PCoA figure, e.g., PCoA_Figure",
  "verbose!","print messages"
)
##########################      Data input      ##########################
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1,check.names = F)
levels_group = sort(unique(group$group))
group$group=factor(group$group,levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列

# if specify the group name as c("GroupA","GroupB","GroupC","GroupD","GroupE")
#color_theme_group = data.frame(
#  Group = c("GroupA","GroupB","GroupC","GroupD","GroupE"),
#  Color = c("#377EB8","#FF7F00","#E41A1C","#4DAF4A","#984EA3")
#) %>%
#  {.[.$Group %in% unique(group$group), 'Color']}

Group_color_data = read.csv(Group_color, sep='\t',header = TRUE, col.names = c('Group', 'Color'),
                             comment.char="", stringsAsFactors = FALSE,check.names = F) %>%
  {.[.$Group %in% unique(group$group), ]}
rownames(Group_color_data) = Group_color_data$Group

if(! dir.exists(Output)){
  dir.create(Output)
}
################################## for循环跑每个类别 ##################################
for (domain in c('A', 'B', 'E', 'V')) {
#domain = 'B'
#Beta_level = 'P'
  print(paste0('Domain: ',domain))
  for (Beta_level in c('P', 'C', 'O', 'F', 'G', 'S')) {
    print(paste0('Beta_level: ',Beta_level))
    for (beta in c('bray', 'jaccard')) {
      print(paste0('Method: ',beta))
      ## PCoA
      label_file_name = paste0(Input, "/", "PCoA.label.", domain, ".", Beta_level, ".", beta, ".csv")
      plot_file_name = paste0(Input, "/",  "PCoA.", domain, ".", Beta_level, ".", beta, ".csv")
      if(file.exists(label_file_name)&file.exists(plot_file_name)){
        label = read.csv(label_file_name, sep=',', header = TRUE, check.names = F)
        file_pcoa = read.csv(plot_file_name, sep=',', header = TRUE, check.names = F)


        levels_group = group %>%
          {.[.$sample_name %in% unique(file_pcoa$sample_name), ]} %>%
          {sort(unique(.$group))}

        Group_color_each_map =  Group_color_data %>%
          {.[.$Group %in% levels_group, ]} %>%
          {.[match(levels_group,rownames(.)),]} %>%
          {mutate(., group = (.$Group))}

        #print(Group_color_each_map)

        plot_pcoa = file_pcoa %>%
          {mutate(.,
                  Group = factor(.$group, levels = levels_group),
                  PCo1 = as.numeric(sprintf("%0.4f", (.$Axis.1))),
                  PCo2 = as.numeric(sprintf("%0.4f", (.$Axis.2)))
          )}

        p3.8 = plot_pcoa %>%
            {ggplot(data = .)+
                stat_ellipse(aes(x=PCo1, y=PCo2, fill=Group),
                            level = 0.95, alpha = 0.15,
                            geom = "polygon") + # 置信椭圆 颜色按照分组，透明度0.15
                geom_point(aes(x=PCo1, y=PCo2, color=Group,
                              text = paste("Run ID: ", sample_name, "\n", sep="")
                ), alpha = 0.7, size=5, show.legend = T)+
                scale_color_manual(values = Group_color_each_map$Color)+
                scale_fill_manual(values = Group_color_each_map$Color)+
                labs(x=paste0("PCo1 (",label$x_label,"%)"),
                    y=paste0("PCo2 (",label$y_label,"%)"))+ # 坐标标签设置
                theme_bw()+
                theme(panel.grid = element_blank()) # 删除网格线
            }
        p3.8 %>%
          ggsave(paste0("PCoA.", domain, ".", Beta_level, ".", beta, ".pdf"),path = Output, .,width = 10, height = 8)


        p3.7 = plot_pcoa %>%
          {ggplot(data=.,aes(x=PCo1, y=PCo2, fill=Group))+ # 导入4列数据sample_name,Axis.1(横轴),Axis.2(纵轴),group
              stat_ellipse(level = 0.95, alpha = 0.15,
                          geom = "polygon")+ # 置信椭圆 颜色按照分组，透明度0.25
              geom_point(aes(text = paste("Run ID: ", sample_name, "\n", sep="")),
                        alpha = 0.7, size=5, color = NA)+
              scale_fill_manual(values = Group_color_each_map$Color)+
              labs(x=paste0("PCo1 (",label$x_label,"%)"),
                  y=paste0("PCo2 (",label$y_label,"%)"))+ # 坐标标签设置
              theme_bw()+
              theme(panel.grid = element_blank()) # 删除网格线
          }
        pp <- p3.7 %>%
          {plotly_build(.)} %>%
          {saveWidget(., file = paste0(Output,"/PCoA.", domain, ".", Beta_level, ".", beta, ".html"),
                                                libdir = "./HTMLdependencies")}
      }
    }
  }
}
