<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row justify="center" class="mt-1">
        <el-col :span="18" class="d-flex">
          <el-select
            v-model="selectVal"
            class="scholarly-select"
            placeholder="Select"
            style="width: 300px; height: 55px"
          >
            <el-option
              v-for="item in selectOptions"
              :key="`select_opt_${item.value}`"
              :label="item.label"
              :value="item.value"
            />

            <!--<el-option label="Bioproject ID" value="Bioproject ID" />
            <el-option label="Sample ID" value="Sample ID" />
            <el-option label="Run ID" value="Run ID" />-->
          </el-select>
          <el-input
            v-model="inputVal"
            :clearable="true"
            placeholder="Search for"
            style="height: 55px"
            @keyup.enter="clickSearchButton"
          />
          <el-button type="primary" :icon="Search" @click="clickSearchButton" />
        </el-col>
      </el-row>
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <h3 class="mt-0">Hot spot</h3>
            <el-divider class="mt-1"></el-divider>
            <div class="hot-spot">
              <template v-if="hotSearchList.length > 0">
                <div
                  v-for="(item, index) in hotSearchList"
                  :key="`hot-search-${index}`"
                  class="d-flex mb-05"
                >
                  <div>
                    <svg-icon
                      icon-class="hotspot2"
                      :width="`10px`"
                      :height="`10px`"
                      class-name="filter"
                    ></svg-icon>
                  </div>
                  <el-tooltip
                    :content="item.title"
                    placement="bottom"
                    :teleported="false"
                  >
                    <div
                      class="cursor-pointer"
                      @click="searchHotWord(item.title)"
                    >
                      {{ item.title }}
                    </div>
                  </el-tooltip>
                </div>
              </template>
              <div v-else>
                <div class="cursor-pointer text-center">No data</div>
              </div>
            </div>
          </div>
          <div class="card mt-1">
            <h3 class="mt-0">Most recent</h3>
            <el-divider class="mt-1"></el-divider>
            <template v-if="mostRecentList.length > 0">
              <div
                v-for="(item, index) in mostRecentList"
                :key="`most_recent_${index}`"
                class="card mt-1"
              >
                <div>
                  <div style="text-align: center">
                    <el-image
                      style="width: 88%; height: 160px"
                      :src="`${imgUrlPre}/most_recent/${item.img}?_t=${new Date().getTime()}`"
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      :hide-on-click-modal="true"
                      :preview-src-list="[
                        `${imgUrlPre}/most_recent/${item.img}?_t=${new Date().getTime()}`,
                      ]"
                      show-progress
                      :initial-index="4"
                      fit="cover"
                    />
                  </div>

                  <div
                    :title="item.title"
                    class="text-main-color text-overflow"
                  >
                    <a
                      :href="initPubmedUrl(item.pmid)"
                      target="_blank"
                      v-text="item.title"
                    ></a>
                  </div>
                  <div
                    v-if="item.journalTitle"
                    class="text-secondary-color text-overflow"
                    :title="initJournal(item, false)"
                    v-text="initJournal(item, false)"
                  ></div>
                </div>
              </div>
            </template>
            <div v-else>
              <div class="cursor-pointer text-center">No data</div>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="card">
            <div
              class="bg-gray radius-8 p-10-15 d-flex align-items-center justify-space-between"
            >
              <div class="d-flex align-items-center">
                <div class="mr-2">Sort By</div>
                <el-radio-group
                  v-model="sortByRadio"
                  @change="() => getDataList()"
                >
                  <el-radio value="Relevance">Relevance</el-radio>
                  <el-radio value="latest">Latest</el-radio>
                  <el-radio value="data_first">Data first</el-radio>
                </el-radio-group>
              </div>

              <div
                style="padding-left: 10px; display: flex; justify-content: end"
              >
                <el-checkbox
                  v-model="checkAll"
                  style="width: 19px"
                  @change="handleSelectAllChange"
                >
                </el-checkbox>
                <el-link
                  :disabled="checkedCount === 0"
                  type="primary"
                  style="font-size: 16px"
                  @click="toBrowse()"
                >
                  Browsing Data
                </el-link>
              </div>
            </div>

            <el-divider class="mt-1"></el-divider>
            <div v-loading="loading">
              <template v-if="dataList.length > 0">
                <div
                  v-for="(item, index) in dataList"
                  :key="`list_item_${index}`"
                  class="pos-relative"
                  :class="['browse-data', 'd-flex', index > 0 ? 'mt-1' : '']"
                >
                  <div class="d-flex browse-content flex-column flex-1">
                    <div class="browse-item">
                      <a
                        :href="initPubmedUrl(item.pmid)"
                        target="_blank"
                        :title="item.title"
                        v-text="item.title"
                      ></a>
                    </div>
                    <div class="text-secondary-color browse-item">
                      <!--<span
                        v-text="
                          `${item.journalTitle} ${item.year} ${item.volume} ${item.issue}`
                        "
                      ></span>-->
                      <span v-text="initJournal(item)"></span>
                    </div>
                    <div
                      class="text-secondary-color browse-item"
                      :title="item.author"
                      v-text="item.author"
                    ></div>
                  </div>
                  <div
                    style="display: flex; justify-content: end"
                    class="bio-project"
                  >
                    <div>
                      <el-tag
                        v-if="item.bioProjectsCount > 0"
                        type="primary"
                        effect="light"
                        style="cursor: pointer"
                        round
                      >
                        <template #default>
                          <el-checkbox
                            v-show="!item.disabled"
                            v-model="item.checked"
                            :disabled="item.disabled"
                          />
                          <span class="font-16" @click="toBrowse(item.pmid)">
                            DATA: {{ item.bioProjectsCount }} BioProjects
                          </span>
                        </template>
                      </el-tag>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="mt-2 text-center">
                  <div class="mt-2">No data found</div>
                </div>
              </template>
            </div>

            <pagination
              v-show="total > 0"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              :auto-scroll="false"
              :total="total"
              @pagination="() => getDataList()"
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- NODE Data Application Dialog -->
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';
  import {
    computed,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRaw,
    toRefs,
  } from 'vue';
  import {
    findHotWordAndMostRecent,
    scholarlyList,
    scholarlySelectItem,
  } from '@/api/scholarly';
  import { useRoute, useRouter } from 'vue-router';
  import { initPubmedUrl, trimStr } from '@/utils';
  import { doAddVisitsLog } from '@/api/visits_log';
  import { VISITS_LOG_FUNC } from '@/constants';

  const { proxy } = getCurrentInstance();

  const router = useRouter();
  const route = useRoute();

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      sortKey: null,
      searchBtnFlag: null,
      searchKeyword: '',
      searchField: '',
      orderByColumn: '',
      pageNum: 1,
      pageSize: 10,
    },
    loading: false,
    selectOptions: [],
    dataList: [],
    initSelectVal: null,
  });
  const sortByRadio = ref('Relevance');
  const selectVal = ref(null);
  // const inputVal = ref('PRJNA727023');
  const inputVal = ref('');
  const {
    total,
    queryParams,
    loading,
    selectOptions,
    dataList,
    initSelectVal,
  } = toRefs(data);
  // const checkAll = ref(false);
  const hotSearchList = ref([]);
  const mostRecentList = ref([]);
  const imgUrlPre = ref(`${import.meta.env.VITE_APP_BASE_API}`);

  // 计算属性：可用的选项
  const availableItems = computed(() =>
    dataList.value.filter(item => !item.disabled),
  );

  // 计算属性：已选中的数量
  const checkedCount = computed(
    () => dataList.value.filter(item => item.checked && !item.disabled).length,
  );

  // 计算属性：是否全选（只考虑可用的选项）
  const checkAll = computed({
    get: () =>
      availableItems.value.length > 0 &&
      availableItems.value.every(item => item.checked),
    set: val => {
      dataList.value.forEach(item => {
        // 只改变可用选项的状态
        if (!item.disabled) {
          item.checked = val;
        } else {
          item.checked = false;
        }
      });
    },
  });

  // 全选复选框变化处理
  const handleSelectAllChange = val => {
    checkAll.value = val;
  };

  function toBrowse(pmid) {
    let ids = [];
    dataList.value.forEach(item => {
      if (item.checked || item.pmid === pmid) {
        let ncbiSraIds = item.ncbiSraIds;
        if (ncbiSraIds) {
          ids.push(...ncbiSraIds);
        }
        let nodeIds = item.nodeIds;
        if (nodeIds) {
          ids.push(...nodeIds);
        }
      }
    });

    if (ids.length === 0) {
      proxy.$modal.msgError('Please select at least one item');
      return;
    }
    /*const loadingInstance = ElLoading.service();
setTimeout(() => {
loadingInstance.close();
}, 1000);*/
    router.push({
      name: 'browse',
      query: {
        ids: ids.join(','),
      },
    });
  }

  function initJournal(item, withDoi = true) {
    // console.log(item);
    // let val = `${item.journalTitle}. ${item.year};${item.volume}(${item.issue}):${item.page}. doi: ${item.doi}`;
    let parts = [];
    if (item.journalTitle) parts.push(item.journalTitle + '.');
    if (item.year) parts.push(item.year);

    let volumeIssue = [];
    if (item.volume) volumeIssue.push(item.volume);
    if (item.issue) volumeIssue.push(`(${item.issue})`);

    if (volumeIssue.length > 0) {
      parts.push(
        volumeIssue.join('') + (item.page ? `:${item.page}` : '') + '.',
      );
    } else if (item.page) {
      parts.push(`:${item.page}.`);
    }

    if (withDoi && item.doi) {
      parts.push(`doi: ${item.doi}`);
    }
    return parts.join(' ');
  }

  function clickSearchButton() {
    queryParams.value.pageNum = 1;
    queryParams.value.pageSize = 10;
    getDataList(true);
  }

  /** 查询列表数据*/
  function getDataList(isSearchBtn = false) {
    loading.value = true;
    dataList.value = [];
    queryParams.value.searchKeyword = inputVal.value;
    queryParams.value.searchField = selectVal.value;
    queryParams.value.sortKey = sortByRadio.value;
    if (isSearchBtn) {
      queryParams.value.searchBtnFlag = '1';
      // 添加日志
      doAddVisitsLog(
        route,
        VISITS_LOG_FUNC.scholarlySearch,
        toRaw(queryParams.value),
      );
    } else {
      queryParams.value.searchBtnFlag = null;
    }

    scholarlyList(queryParams.value)
      .then(response => {
        // console.log(response);
        loading.value = false;
        total.value = response.total;
        dataList.value = response.rows;
      })
      .catch(() => {
        total.value = 0;
        loading.value = false;
      });
  }

  function initHotSearchAndMostRecent() {
    hotSearchList.value = [];
    mostRecentList.value = [];
    findHotWordAndMostRecent().then(res => {
      hotSearchList.value = res.hotWordList;
      mostRecentList.value = res.mostRecentList;
      // console.log(res);
    });
  }

  function searchHotWord(title) {
    inputVal.value = title;
    selectVal.value = initSelectVal.value;
    clickSearchButton();
  }

  onMounted(() => {
    initHotSearchAndMostRecent();
    scholarlySelectItem()
      .then(res => {
        for (let i = 0; i < res.length; i++) {
          let item = res[i];
          if (item.selected) {
            initSelectVal.value = item.value;
            selectVal.value = item.value;
            break;
          }
        }
        selectOptions.value = res;
      })
      .then(() => {
        let queryKeyword = trimStr(proxy.$route.query.keyword);
        if (queryKeyword) {
          // 从首页进行文献语义检索
          inputVal.value = queryKeyword;
        }
        getDataList();
      });
  });

  /*watch(sortByRadio, (newVal, oldVal) => {
console.log(newVal, oldVal);
});*/
</script>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .submit-page {
    padding: 120px 0 45px 0;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .hot-spot {
    .cursor-pointer {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  .filter {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
  }

  .browse-data {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 10px 10px;
  }

  .browse-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    max-width: 100%;
    margin-right: 1rem;
  }

  .browse-item {
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    margin-bottom: 5px; /* 为每个项添加间距 */
  }

  .el-tag {
    //display: flex;
    //justify-content: end;
    ///* padding-top: 25px; */
    //position: absolute;
    //right: 10px;
    //top: 35%;
    background-color: #ebf1ff;

    :deep(.el-tag__content) {
      color: #335cff;
      font-size: 16px;
    }
  }

  .el-select.scholarly-select {
    border-radius: 0;

    :deep(.el-select__wrapper) {
      height: 55px;
      background: #f5f7fa;
      box-shadow: none;
      border: 1px solid #e5e7eb;
      border-right: none;
      border-radius: 4px 0 0 4px;
    }

    :deep(.el-select__placeholder) {
      font-size: 16px;
    }
  }

  .el-input {
    border: 1px solid #e5e7eb;
    border-left: 0;
    border-radius: 0;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
  }

  :deep(.el-input__inner),
  :deep(.el-checkbox__label) {
    font-size: 16px !important;
  }

  .el-button {
    background-color: #1e7cb2;
    width: 65px;
    height: 55px;
    border-radius: 0 4px 4px 0;
  }

  .el-checkbox {
    height: 25px;
  }

  :deep(.el-radio__input.is-checked .el-radio__inner) {
    border-color: #1e7cb2 !important;
    background-color: #1e7cb2 !important;
  }

  :deep(.el-radio__label) {
    color: #333333;
  }

  .hot-spot-icon {
    color: #1e7cb2;
    margin-top: 2px;
    margin-right: 2px;
  }

  .bio-project {
    display: flex;
    justify-content: end;
    position: absolute;
    right: 10px;
    top: 35%;
  }

  :deep(.el-checkbox__inner) {
    border: 1px solid #aaa8a8;
  }
</style>
<style>
  .hide-select-all .el-table__header-wrapper .el-checkbox {
    display: none;
  }
</style>
