###
#' @Date: 2024-03-26 12:38:36
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-03-26 15:12:40
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v7/M2S3.density.cmd.R
#' @Description:
###

suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
GetoptLong(
"inputfile=s", "Nitrosopumilus.tsv",
"outputfile=s", "e.g., Nitrosopumilus.density.tsv",
"verbose!","print messages"
)

A.G.percent = read.csv(inputfile, sep = '\t', row.names = 1, header = T,check.names = F)

A.G.percent_density = density(A.G.percent$Avg_fold/10000)
# plot(A.G.percent_density)

density_data <- data.frame(x = A.G.percent_density$x, y = A.G.percent_density$y)
#library(ggplot2)
#ggplot(density_data) + geom_point(aes(x = x, y = y))
density_data <- rbind(density_data, A.G.percent_density$bw)

write.table(density_data, outputfile, sep = '\t', col.names = T, row.names = F,quote = F,
            fileEncoding = "UTF-8", na = "")


# A.G.percent$Avg_fold %>%   {.[.>=median(.)]} %>%   {median(.)}
# summary(A.G.percent$Avg_fold/10000)
