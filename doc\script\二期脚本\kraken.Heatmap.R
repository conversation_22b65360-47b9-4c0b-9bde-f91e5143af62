###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangL<PERSON>
#' @LastEditTime: 2022-11-11 23:10:11
#' @FilePath: /R/Rscript.kraken/kraken.Heatmap.R
#' @Description:

suppressMessages(library(GetoptLong))
suppressMessages(library(pheatmap))
suppressMessages(library(tidyverse))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Input=s", "input Dir for Heatmap Table, e.g., Heatmap_Table",
  "Output=s", "output Dir for Heatmap figure, e.g., Heatmap_Figure",
  "verbose!","print messages"
)

##########################      Data input      ##########################
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1)
levels_group = sort(unique(group$group))
group$group=factor(group$group,
                            levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列
## 热图
anno_row = group %>%
  {subset(., select=get('group'))}
ann_colors = list(
  group = c(GroupA = "#A3A500", GroupB = "#00BF7D", GroupC = "#00B0F6", GroupD = "#E76BF3", GroupE = "#F8766D")
)
if(! dir.exists(Output)){
  dir.create(Output)
}
################################## for循环跑每个类别 ##################################

for (domain in c('A', 'B', 'E', 'V')) {
  for (beta in c('bray', 'jaccard')) {
    print(paste0('Method: ',beta))
    ## 热图
    read.csv(paste0(Input, "/", "Heatmap.", domain, ".", beta, ".csv"),sep=',',header = TRUE, row.names = 1) %>%
      {pheatmap(.,
                    method = 'average',
                    cutree_rows = length(unique(group$group)),
                    cutree_cols = length(unique(group$group)),
                    na_col='grey',
                    cluster_rows = T,
                    cluster_cols = T,###是否对行列聚类
                    show_rownames = T,show_colnames = T,###是否显示行名列名
                    cellwidth = 7,cellheight = 6,###固定每个cell的宽和高
                    display_numbers = F,####是否显示每个单元格的值
                    fontsize_row = 6,
                    fontsize_col	= 5,
                    angle_col = 45,
                    annotation_col = anno_row,
                    annotation_colors = ann_colors,
                    annotation_row = anno_row
      )} %>%
      {ggsave(paste0("Heatmap.", domain, ".", beta, ".png"),
                    path = Output, .,
                    width = 14, height = 11)
      }
  }
}
