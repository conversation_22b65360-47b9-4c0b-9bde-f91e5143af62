###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-11-03 11:25:30
#' @FilePath: /liliuyang/R/Rscript.MASH/202309/kraken.Count.cmd.R
#' @Description: 新增了rc_cal函数,并计算了rc_tax,导出了新增结果,注意输入命令行增加: --Count,  质粒在三个域都有出现。所有总体表格需要对种名进行标准化.
###

suppressMessages(library(GetoptLong))
suppressMessages(library(tidyverse))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Source=s", "Total abundance table, e.g., kraken2_taxonomic_profiles.tsv",
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Count=s", "output Dir for Total Reads Count Table, e.g., Count_Table",
  "verbose!","print messages"
)

suppressMessages(library(reshape2))
suppressMessages(library(vegan))
suppressMessages(library(tidyverse))
suppressMessages(library(ape))
##########################      底层数据自定义计算函数     ##########################
rc_cal = function(data = taxonomic_profiles,
                  domain_level = 'B',
                  tax_level = 'P'){
  #  从总的[物种row-样品col]表格,获取各个分类水平的[样品row-物种col]的reads count表格
  # data should be the species-sample table, and this table will be transferred to be a sample-species table with reads count
  # domain_level can be any of the 'A', 'B', 'E', 'V'
  # tax_level can be any of the 'P', 'C', 'O', 'F', 'G', 'S'
  # 取分类的子集
  tax_df = data %>%
    {.[.$Domain %in% domain_level,, drop = F]} %>%
    {.[.$Level %in% tax_level,, drop = F]}
  rownames(tax_df) = tax_df$Clade_name
  # 保留数值列
  tax_df.t = tax_df %>%
    {subset(., select=-c(get('Domain')))} %>%
    {subset(., select=-c(get('Level')))} %>%
    {subset(., select=-c(get('Clade_name')))} %>%
    {.[rowSums(.)>0,, drop = F]} %>%
    {.[,colSums(.)>0, drop = F]} %>%
    {as.data.frame(t(.))}
  return(tax_df.t)
}

##########################      Data input      ##########################
taxonomic_profiles <- read.csv(Source, sep = '\t', quote='',check.names = F) %>%
    {mutate(., Clade_name = paste0(.$Domain, "__", gsub(" ", "_", .$Clade_name)))}

group = read.csv(Group,sep='\t',header = TRUE,row.names = 1,check.names = F)

levels_group = sort(unique(group$group))
group$group=factor(group$group, levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列

group = group %>%
    {.[rownames(.) %in% colnames(taxonomic_profiles), , drop = F]}

################################## for循环跑每个类别 ##################################
for (variable in c(Count)) {
  if(! dir.exists(variable)){
    dir.create(variable)
  }
}

for (taxonomy in c('S','G','F','O','C','P')) {
  print(paste0('Taxonomy: ',taxonomy))

  rc_tax = rc_cal(taxonomic_profiles, c('A', 'B', 'E', 'V'), taxonomy)
  # output the 24 tables
  rc_tax %>%
    {as.data.frame(t(.))} %>%
    {rownames_to_column(., var='taxon')} %>%
    {write.table(.,
                  paste0(Count, "/", "All", ".", taxonomy, ".count.tsv"),
                  sep = "\t", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
}
