###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-03-25 21:00:27
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v7/M1S6.Module_Sample.group_by_pathway.cmd.R
#' @Description: transfer the count to relative abundance

suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("tidyverse","reshape2","dplyr", "stringr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0('\n成功载入包: ',pkg),'\n\n')
}
ipak(packages)
GetoptLong(
  "in_KO_pathway_info=s", "in_KO_pathway_info",
  "in_KO_Sample_wide=s", "in_KO_Sample_wide",
  "out_dir_ko_sample_by_pathway=s", "output directory",
  "prefix=s", "prefix",
  "table_feature=s", "table_feature, 'abundance' or 'presence'",
  "plus_scale_method=s", "plus_scale_method, e.g., 'mean', 'max', or 'min'",
  "comma_scale_method=s", "comma_scale_method, e.g., 'max' for presence/absence data, or 'sum' for abundance data",
  "verbose!","print messages"
)

#   read_and_process_KO_table -->    process_module_structure   -->   process_module_brackets
#                                      |
#                                      V
#                             process_module_definition
#                                      |
#                                      V
#                           process_module_loop_plu_comma
#                                      |
#                                      V
#               process_module_loop_comma      process_module_loop_plus
#                    /          |            \                     \
#                  /            |              \                     \
#   process_step_plus   process_step_direct    process_step_comma     process_step_space

##############ata_cal##############
ata_cal = function(data = KO_Sample_table){
  # Calculate the average transformed abundance
  # mean relative abundance Calculation
  # KO_Sample_table: row,KO; col,samples.
  data$Mean_RA = apply(data, 1, mean) # 对每一行计算平均值
  scaled_df = data # 创建一个新的数据框用于存储结果
  # 对数据框中的每一列进行操作
  for(col in colnames(data)){
    scaled_df[[col]] <- ifelse(((data[[col]] != 0) & (data$Mean_RA != 0)), log2(data[[col]]/data$Mean_RA), 0)
  }
  scaled_df = select(scaled_df, -Mean_RA)
  return(scaled_df)
}

##############read_and_process_pathway_infor##############
read_and_process_pathway_infor <- function(in_KO_pathway_info) {
  # Use tryCatch function to handle potential errors
  tryCatch({
    # Read the file
    pathway_infor = read.csv(in_KO_pathway_info, sep='\t',header = TRUE, check.names = F)

    # Check if the file was successfully imported
    if (!is.null(pathway_infor)) {
      cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] Pathway information file successfully imported.\n\n'))
    } else {
      stop(paste0('\n\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] *** Pathway information file is null, please check the input file path and format ***\n\n'))
    }
    # 删除列a中含有NA的行
    pathway_infor <- pathway_infor[!(pathway_infor$Module_Entry %in% 'NA'), , drop = F]
    pathway_infor <- pathway_infor[!(pathway_infor$Module_Entry %in% ''), , drop = F]
    pathway_infor <- pathway_infor[!is.na(pathway_infor$Module_Entry), , drop = F]

    Module_list <- unique(pathway_infor$Module_Entry)
    if (length(Module_list) > 0) {
      cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] There are ',length(Module_list),' Modules in the Pathway information file.\n\n'))
      cat(Module_list)
      cat('\n\n')
    } else {
      stop(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] *** No Module detected, please check the input file path and format ***\n\n'))
    }

    return(pathway_infor)
  }, error = function(e) {
    stop(paste0('\n\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] *** Pathway information file import failed, please check the input file path and format ***\n\n'))
  })
}


##############read_and_process_KO_table##############
read_and_process_KO_table <- function(in_KO_Sample_wide, pathway_infor) {
  # Use tryCatch function to handle potential errors
  tryCatch({
    # Read the file
    Sample_KO = read.csv(in_KO_Sample_wide, sep='\t',header = TRUE, row.names = 1, check.names = F)

    # Check if the file was successfully imported
    if (!is.null(Sample_KO)) {
      cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] File successfully imported.\n\n'))
    } else {
      stop(paste0('\n\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] *** File import failed, please check the input file path and format ***\n\n'))
    }

    KO_list <- unique(pathway_infor$Orthology_Entry)

    # Check the intersection of the first column and KO_list
    common_genes <- intersect(rownames(Sample_KO), KO_list)

    if (length(common_genes) > 0) {
      cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] There is an intersection between the first column of input file and KO list, with ',length(common_genes),' KOs:\n\n'))
      cat(common_genes)
      cat('\n\n')
    } else {
      stop(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] *** There is no intersection between the first column of the input file and KO list, please check the input file path and format ***\n\n'))
    }

    # Use the mutate function to add a new column Orthology_Entry, the value of which is the row names of the data frame
    Sample_KO <- mutate(Sample_KO, Orthology_Entry = rownames(Sample_KO))
    # Filter out the rows where Orthology_Entry is in pathway_infor$Orthology_Entry
    Sample_KO <- Sample_KO[rownames(Sample_KO) %in% unique(pathway_infor$Orthology_Entry), , drop = F]

    return(Sample_KO)
  }, error = function(e) {
    stop(paste0('\n\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] *** File import failed, please check the input file path and format ***\n\n'))
  })
}

##############process_module_structure##############
process_module_structure <- function(pathway_infor, Sample_KO, module) {
  # 根据Module_Entry筛选pathway_infor中的信息
  each_pathway_infor = pathway_infor %>%
    {.[(.$Module_Entry) %in% module, c("Orthology_Entry","Module_Entry","Definition"),drop = F]} %>%
    {unique(.)}

  # 根据each_pathway_infor中的Orthology_Entry筛选Sample_KO中的信息
  sub_Sample_KO = Sample_KO %>%
    {.[rownames(.) %in% unique(each_pathway_infor$Orthology_Entry),,drop = F]}

  # 如果sub_Sample_KO中有Orthology_Entry
  if (length(sub_Sample_KO$Orthology_Entry)>0){
    cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] Running Module Entry: ',module,'\n\n'))
    # 合并each_pathway_infor和sub_Sample_KO
    sub_Sample_KO_pathway = sub_Sample_KO %>%
      merge(., each_pathway_infor, by = 'Orthology_Entry', all.x = T) %>%
      {
        rownames(.) = .$Orthology_Entry
        (.)
      }
  } else {
    sub_Sample_KO_pathway = data.frame()
  }
  return(sub_Sample_KO_pathway)
}

# sub_Sample_KO_pathway = process_module_structure(pathway_infor, Sample_KO, module = 'M00563')

##############process_module_definition##############
process_module_definition <- function(sub_Sample_KO_pathway) {
  # 使用unique函数去除Definition列中的重复值
  unique_definitions <- unique(sub_Sample_KO_pathway$Definition)
  # 使用gsub函数删除字符串中的特定模式
  # 删除"-- "模式
  step1 <- gsub("-- ", "", unique_definitions)
  # 删除" --"模式
  step2 <- gsub(" --", "", step1)
  # 删除"-K数字"模式
  step3 <- gsub("-K\\d+", "", step2)
  # 负一个括号的情况# M00011 -(K00242,K18859,K18860)
  step4 <- gsub("-\\(.*?\\)", "", step3)
  # 将多个连续的空格替换为一个空格
  step5 <- gsub("\\s+", " ", step4)
  # 将"(K数字)"模式替换为"K数字"
  result <- gsub("\\((K\\d+)\\)", "\\1", step5)

  cat(paste0('\nProcessing module steps: ',unique_definitions,'\n\n'))
  cat(paste0('After omitting minus KOs: ',result,'\n\n'))

  return(result)
}


##############convert_abundance_to_presence##############
convert_abundance_to_presence <- function(module_abundance) {
  module_abundance %>%
    {
      rownames(.) = (.$Orthology_Entry)
      (.)
    } %>%
    {select(., -c(Orthology_Entry))} %>%
    # {apply(., 2, function(x) ifelse(x > 0, 1, 0))} %>%
    {ifelse(as.matrix(.) > 0, 1, 0)} %>%
    {as.data.frame(.)} %>%
    {mutate(.,
            Orthology_Entry = rownames(.)
    )}
}

##############add_rows_if_not_exists##############
add_rows_if_not_exists <- function(module_abundance, add_rows = c("K14126","K14128","K14127")) {
  for (row_name in add_rows) {
    if (!(row_name %in% rownames(module_abundance))) {
      module_abundance[row_name, ] <- 0
    }
  }
  return(module_abundance)
}

###############    group_ko_by_module     ##############
group_ko_by_module <- function(pathway_infor, Sample_KO_abundance) {
  replace_values <- function(df) {
    df[] <- lapply(df, function(x) {
      ifelse(x == 1, df$Orthology_Entry, ifelse(x == 0, "", x))
    })
    return(df)
  }
  Sample_ko_group_by_module = Sample_KO_abundance %>%
    {convert_abundance_to_presence(.)} %>%
    {select(., -c(Orthology_Entry))} %>%
    {add_rows_if_not_exists(., add_rows = unique(pathway_infor$Orthology_Entry))} %>%
    {mutate(., Orthology_Entry = rownames(.))} %>%
    {merge(., unique(pathway_infor[, c("Orthology_Entry","Module_Name")]), all.x = T, by = 'Orthology_Entry')} %>%
    {replace_values(.)} %>%
    {select(., -Orthology_Entry)}  %>%
    group_by(Module_Name) %>%
    summarize(across(everything(), ~paste(.[. != ""], collapse = " "))) %>%
    ungroup(.) %>%
    column_to_rownames(., 'Module_Name')
  return(Sample_ko_group_by_module)
}

##############process_step_direct##############

process_step_direct <- function(module_abundance, KOs = c("K14126","K14128","K14127")) {
  # 按照无特殊处置
  cat(paste0('\t\tRunning direct KOs: ',KOs,'\n'))
  module_abundance %>%
    {
      rownames(.) = (.$Orthology_Entry)
      (.)
    } %>%
    {select(., -c(Orthology_Entry, Module_Entry, Definition))} %>%
    {add_rows_if_not_exists(., add_rows = KOs)} %>%
    {mutate(.,
            Orthology_Entry = rownames(.),
            Module_Entry = unique(module_abundance$Module_Entry),
            Definition = unique(module_abundance$Definition)
    )} %>%
    {.[rownames(.) %in% KOs, , drop = F]}
}

# sub2_Sample_KO_pathway <- process_step_direct(module_abundance = sub_Sample_KO_pathway,
#                                               c("K14126","K14128","K14127"))


############## process_step_plus##############

# 按照"+"号的处置, 多个内容取平均值
# ***求平均值需要注意，因为表格缺少KO丰度在所有样本中都为0的一些KO，计算时使用已有的KO丰度的和除以KO的个数

process_step_plus <- function(module_abundance, KOs = c("K14126+K14127+K14128"), aggregrate_rowname,
                               step_count = 1, plus_scale_method = "mean") {
  # plus进入循环的加上 new_step_name = paste0(aggregrate_rowname, '_', step_count)
  # 按照"+"号的处置
  KOs_scale <- strsplit(KOs, "\\+")[[1]]
  cat(paste0('\t\tRunning KOs plus: ',aggregrate_rowname," = ",KOs_scale,'\n'))
  abundance_table = module_abundance %>%
    {
      rownames(.) = (.$Orthology_Entry)
      (.)
    } %>%
    {select(., -c(Orthology_Entry, Module_Entry, Definition))} %>%
    {add_rows_if_not_exists(., add_rows = KOs_scale)} %>%
    {.[rownames(.) %in% KOs_scale, , drop = F]}

  # 只剩一列会自动变成行需要转置回来
  if (plus_scale_method == "mean") {
    abundance_table_scale = abundance_table %>% {t(colSums(.)/length(KOs_scale))}
  } else if (plus_scale_method == "min") {
    abundance_table_scale = abundance_table %>% {t(apply(., 2, min))}
  } else if (plus_scale_method == "max") {
    abundance_table_scale = abundance_table %>% {t(apply(., 2, max))}
  } else {
    stop(paste("Unknown plus scale method:", plus_scale_method))
  }

  result = abundance_table_scale %>%
    {as.data.frame((.), row.names = aggregrate_rowname)} %>%
    {mutate(.,
            Orthology_Entry = rownames(.),
            Module_Entry = unique(module_abundance$Module_Entry),
            Definition = unique(module_abundance$Definition))}
  step_count = step_count + 1
  return(list(abundance_table = result, step_count = step_count))
}

# sub2_Sample_KO_pathway =  process_step_plus(module_abundance = sub_Sample_KO_pathway,
#                                            c("K14126+K14128"), 'step_1')

############## process_step_comma##############

# 按照","号的处置, 多个内容取和
process_step_comma <- function(module_abundance, KOs = c("K14126,K14127,K14128"), aggregrate_rowname,
                               step_count = 1, comma_scale_method = "sum") {
  # 按照","号的处置
  # comma进入循环的加上 new_step_name = paste0(aggregrate_rowname, '_', step_count)
  KOs_scale <- strsplit(KOs, ",")[[1]]
  cat(paste0('\t\tRunning KOs comma: ',aggregrate_rowname," = ",KOs_scale,'\n'))
  abundance_table = module_abundance %>%
    {
      rownames(.) = (.$Orthology_Entry)
      (.)
    } %>%
    {select(., -c(Orthology_Entry, Module_Entry, Definition))} %>%
    {add_rows_if_not_exists(., add_rows = KOs_scale)} %>%
    {.[rownames(.) %in% KOs_scale, , drop = F]}

  # 只剩一列会自动变成行需要转置回来
  if (comma_scale_method == "sum") {
    abundance_table_scale = abundance_table %>% {t(colSums(.))}
  } else if (comma_scale_method == "max") {
    abundance_table_scale = abundance_table %>% {t(apply(., 2, max))}
  } else {
    stop(paste("Unknown comma scale method:", comma_scale_method))
  }

  result = abundance_table_scale %>%
    {as.data.frame((.), row.names = aggregrate_rowname)} %>%
    {mutate(.,
            Orthology_Entry = rownames(.),
            Module_Entry = unique(module_abundance$Module_Entry),
            Definition = unique(module_abundance$Definition)
    )}
  step_count = step_count + 1
  return(list(abundance_table = result, step_count = step_count))
}


# sub2_Sample_KO_pathway <-  process_step_comma(module_abundance = sub_Sample_KO_pathway,
#                                            c("K14126,K14127,K14128"), 'step_2')

############## process_step_space ##############

# 按照"+"号的处置, 多个内容取平均值
# ***求平均值需要注意，因为表格缺少KO丰度在所有样本中都为0的一些KO，计算时使用已有的KO丰度的和除以KO的个数

process_step_space <- function(module_abundance, KOs = c("K14126 K14127 K14128"), aggregrate_rowname, step_count = 1) {
  # space不进入循环loop，不加 new_step_name = paste0(aggregrate_rowname, '_', step_count)
  # 按照"+"号的处置
  KOs_scale <- strsplit(KOs, " ")[[1]]
  cat(paste0('\t\tRunning KOs space: ',aggregrate_rowname," = ",KOs_scale,'\n'))
  abundance_table = module_abundance %>%
    {
      rownames(.) = (.$Orthology_Entry)
      (.)
    } %>%
    {select(., -c(Orthology_Entry, Module_Entry, Definition))} %>%
    {add_rows_if_not_exists(., add_rows = KOs_scale)} %>%
    {.[rownames(.) %in% KOs_scale, , drop = F]} %>%
    {t(colSums(.)/length(KOs_scale))} %>%
    # 只剩一列会自动变成行需要转置回来
    {as.data.frame((.), row.names = aggregrate_rowname)} %>%
    {mutate(.,
            Orthology_Entry = rownames(.),
            Module_Entry = unique(module_abundance$Module_Entry),
            Definition = unique(module_abundance$Definition))}
  step_count = step_count + 1
  return(list(abundance_table = abundance_table, step_count = step_count))
}

##############process_module_loop_plus##############
process_module_loop_plus <- function(KO_vector,
                                     module_abundance,
                                     process_step_plus,
                                     process_step_direct,
                                     aggregrate_rowname = 'step_1',
                                     step_count = 1) {
  # 在这个函数中，KO_vector是一个向量，包含了你想要处理的所有KOs；
  # module_abundance，包含了你的原始数据；
  #  process_step_plus和process_step_direct是两个函数，用于处理包含"+“的KOs和不包含”+"的KOs。
  # 这个函数会遍历KO_vector中的每一个KOs，根据KOs中是否包含"+"，选择不同的处理方式。
  # 然后，它会将处理后的结果合并到一个新的数据框result中。最后，这个函数会返回result。
  # (K22516+K14126 K22517 K22518+K14129) 先算 K22516+K14126和K22518+K14129，结果有三行
  abundance_table = data.frame()
  abundance_table.tmp = data.frame()
  for (KOs in KO_vector) {
    has_plus <- grepl("\\+", KOs)
    if (has_plus) {
      # 按照"+"号的处置
      # new_step_name = paste0(aggregrate_rowname, '_', step_count)
      result =  process_step_plus(module_abundance, KOs, aggregrate_rowname = paste0(aggregrate_rowname, '_', step_count),
                                  step_count, plus_scale_method)
      abundance_table.tmp = result[['abundance_table']]
      step_count = result[['step_count']]
    }
    else {
      # 没有"+"号直接取交集
      abundance_table.tmp = process_step_direct(module_abundance, KOs)
    }
    abundance_table <- rbind(abundance_table, abundance_table.tmp) %>% unique(.)
  }
  return(list(abundance_table = abundance_table, step_count = step_count))
}
# 使用这个函数
# sub2_Sample_KO_pathway <- process_module_loop_plus(
# c("K03388", "K03389+K03390+K14083","K14126+K14127", "K14128"),
# module_abundance = sub_Sample_KO_pathway,  process_step_plus, process_step_direct)

##############process_module_loop_comma##############
process_module_loop_comma <- function(KO_vector,
                                      module_abundance,
                                      process_step_comma,
                                      process_step_direct,
                                      aggregrate_rowname = 'step_1',
                                      step_count = 1) {
  # 在这个函数中，KO_vector是一个向量，包含了你想要处理的所有KOs；
  # module_abundance，包含了你的原始数据；
  #  process_step_comma和process_step_direct是两个函数，用于处理包含",“的KOs和不包含”,"的KOs。
  # 这个函数会遍历KO_vector中的每一个KOs，根据KOs中是否包含","，选择不同的处理方式。
  # 然后，它会将处理后的结果合并到一个新的数据框result中。最后，这个函数会返回result。
  # (K22516,K14126 K22517 K22518,K14129) 先算 K22516,K14126和K22518,K14129
  abundance_table = data.frame()
  abundance_table.tmp = data.frame()
  for (KOs in KO_vector) {
    has_comma <- grepl(",", KOs)
    if (has_comma) {
      result =  process_step_comma(module_abundance, KOs, aggregrate_rowname = paste0(aggregrate_rowname, '_', step_count),
                                   step_count, comma_scale_method)
      abundance_table.tmp = result[['abundance_table']]
      step_count = result[['step_count']]
    }
    else {
      # 没有"+"号直接取交集
      abundance_table.tmp = process_step_direct(module_abundance, KOs)
    }
    abundance_table <- rbind(abundance_table, abundance_table.tmp) %>% unique(.)
  }
  return(list(abundance_table = abundance_table, step_count = step_count))
}

##############process_module_loop_plu_comma##############
process_module_loop_plu_comma <- function(KO_vector,
                                          module_abundance,
                                          process_step_plus,
                                          process_step_comma,
                                          process_step_direct,
                                          aggregrate_rowname = 'step_1',
                                          step_count = 1) {
  # 在这个函数中，KO_vector是一个向量，包含了你想要处理的所有KOs；
  # module_abundance，包含了你的原始数据；
  #  process_step_plus和process_step_direct是两个函数，用于处理包含",“的KOs和不包含”,"的KOs。
  # 这个函数会遍历KO_vector中的每一个KOs，根据KOs中是否包含","，选择不同的处理方式。
  # 然后，它会将处理后的结果合并到一个新的数据框result中。最后，这个函数会返回result。
  abundance_table = data.frame()
  abundance_table.tmp = data.frame()
  # (K14126+K14128,K22516+K00125,K00126)
  for (KOs in KO_vector) {
    # 检查字符串中是否包含","和"+"
    has_comma <- grepl(",",  KOs)
    has_plus <- grepl("\\+", KOs)
    # K14126+K14128,K22516+K00125,K00126
    if (has_plus && has_comma) {
    # 每个sub循环"+",后","
      KO_vector <- strsplit(KOs, ",")[[1]]
      result <- process_module_loop_plus(KO_vector,module_abundance, process_step_plus,process_step_direct,
                                        aggregrate_rowname,step_count)
      KO_scale = paste(rownames(result[['abundance_table']]), collapse = ',')
      step_count = result[['step_count']]
      result_list <-  process_step_comma(result[['abundance_table']], KO_scale,
                                          aggregrate_rowname = paste0(aggregrate_rowname, '_', step_count), step_count, comma_scale_method)
      abundance_table.tmp = result_list[['abundance_table']]
      step_count = result_list[['step_count']]
    }
    # 如果字符串中只包含"," K22516,K14126
    else if (has_comma) {
      # 按照","号的处置
      result_list =  process_step_comma(module_abundance, KOs,
                                        aggregrate_rowname = paste0(aggregrate_rowname, '_', step_count), step_count, comma_scale_method)
      abundance_table.tmp = result_list[['abundance_table']]
      step_count = result_list[['step_count']]
    }
    # 如果字符串中只包含"+" K22516+K00125
    else if (has_plus) {
      # 按照"+"号的处置
      result_list =  process_step_plus(module_abundance, KOs,
                                        aggregrate_rowname = paste0(aggregrate_rowname, '_', step_count), step_count, plus_scale_method)
      abundance_table.tmp = result_list[['abundance_table']]
      step_count = result_list[['step_count']]
    }
    else {
      # 没有"+"号直接取交集
      abundance_table.tmp = process_step_direct(module_abundance, KOs)
    }
    abundance_table <- rbind(abundance_table, abundance_table.tmp) %>% unique(.)
  }
  return(list(abundance_table = abundance_table, step_count = step_count))
}

##############remove_outer_brackets##############
# 定义一个函数来检查一个字符串最外面是否有()，如果有则去除
remove_outer_brackets <- function(s) {
  # 使用正则表达式检查字符串最外面是否有()
  if (grepl("^\\(.*\\)$", s)) {
    # 如果有，则去除
    s <- sub("^\\((.*)\\)$", "\\1", s)
  }
  return(s)
}

##############process_module_step##############
# 定义一个函数来处理字符串
process_module_step <- function(module_abundance, KO_string = "K03388,K03389+K03390+K14083,K14126+K14127,K14128",
                           aggregrate_rowname = 'bracket_1', step_count = 1) {
  KO_string = remove_outer_brackets(KO_string)
  # 检查字符串中是否包含","和"+"
  has_comma <- grepl(",", KO_string)
  has_plus <- grepl("\\+", KO_string)
  has_space <- grepl(" ", KO_string)
  # 如果字符串中既包含","也包含"+" ，也包含" "
  # (K14126+K14128,K22516+K00125 K00126 K22516,K14126 K22516+K00125)
  if ((has_plus && has_comma && has_space)) {
    # 按照" "分隔符，拆成不同的sub
    # (K14126+K14128,K22516+K00125 K00126) 拆成(K14126+K14128,K22516+K00125) (K00126)
    #print('Warning: has_plus && has_comma && has_space !!!')
    KO_vector <- strsplit(KO_string, " ")[[1]]
    result <- process_module_loop_plu_comma(KO_vector,module_abundance, process_step_plus,process_step_comma,process_step_direct,
                                          aggregrate_rowname,step_count)
    KO_scale = paste(rownames(result[['abundance_table']]), collapse = '+')
    result_list <-  process_step_plus(result[['abundance_table']], KO_scale, aggregrate_rowname, result[['step_count']], plus_scale_method)
  }
  else if (has_plus && has_comma) {
    # 每个sub循环"+",后","
    KO_vector <- strsplit(KO_string, ",")[[1]]
    result <- process_module_loop_plus(KO_vector,module_abundance, process_step_plus,process_step_direct,
                                      aggregrate_rowname,step_count)
    KO_scale = paste(rownames(result[['abundance_table']]), collapse = ',')
    result_list <-  process_step_comma(result[['abundance_table']], KO_scale, aggregrate_rowname, result[['step_count']], comma_scale_method)
  }
  else if (has_plus && has_space) {
    # 每个sub循环"+",后" "
    KO_vector <- strsplit(KO_string, " ")[[1]]
    result <- process_module_loop_plus(KO_vector,module_abundance, process_step_plus,process_step_direct,
                                      aggregrate_rowname,step_count)
    KO_scale = paste(rownames(result[['abundance_table']]), collapse = ' ')
    result_list <-  process_step_space(result[['abundance_table']], KO_scale, aggregrate_rowname, result[['step_count']])
  }
  else if (has_comma && has_space) {
    # 每个sub循环",",后" "
    KO_vector <- strsplit(KO_string, " ")[[1]]
    result <- process_module_loop_comma(KO_vector,module_abundance, process_step_comma,process_step_direct,
                                      aggregrate_rowname,step_count)
    KO_scale = paste(rownames(result[['abundance_table']]), collapse = ' ')
    result_list <-  process_step_space(result[['abundance_table']], KO_scale, aggregrate_rowname, result[['step_count']])
  }
  # 如果字符串中只包含"," (K14126,K22516)
  else if (has_comma) {
    # 按照","号的处置
    result_list <-  process_step_comma(module_abundance, KO_string, aggregrate_rowname, step_count, comma_scale_method)
  }
  # 如果字符串中只包含"+" (K14126+K14128)
  else if (has_plus) {
    # 按照"+"号的处置
    result_list <-  process_step_plus(module_abundance, KO_string, aggregrate_rowname, step_count, plus_scale_method)
  }
  # 如果字符串中只包含" " (K14126 K22516)
  else if (has_space) {
    # 按照" "号的处置
    result_list <-  process_step_space(module_abundance, KO_string, aggregrate_rowname, step_count)
  # 如果字符串中只包含"一个元素" (K14126)
  } else {
    abundance_table = process_step_direct(module_abundance, KO_string)
    result_list = list(abundance_table = abundance_table, step_count = step_count)
    #stop("Please check Module Definition!")
  }
  return(result_list)
}

# 测试这个函数
# KO_string_scale = process_module_step(module_abundance = sub_Sample_KO_pathway,
#"K03388,K03389+K03390+K14083,K14126+K14127,K14128", aggregrate_rowname = 'step_1')
# KO_string_scale = process_module_step(sub_Sample_KO_pathway, c("K14126+K14128"),
#aggregrate_rowname = 'step_1')
# KO_string_scale = process_module_step(sub_Sample_KO_pathway,
#"K03388,K03389+K03390+K14083,K14126+K14127,K14128", aggregrate_rowname = 'step_1')


##############extract_inner_brackets##############
# 定义一个函数来提取最内层的括号
extract_inner_brackets <- function(s) {
  str_extract_all(s, "\\([^()]*\\)")[[1]]
}

##############escape_special_chars##############
# 定义一个函数来转义一个字符串中的所有特殊字符
escape_special_chars <- function(s) {
  # 定义所有需要转义的特殊字符
  special_chars <- c("(", ")", "+", "-")

  # 对每一个特殊字符进行转义
  for (char in special_chars) {
    s <- gsub(char, paste0("\\", char), s, fixed = TRUE)
  }

  return(s)
}

##############process_module_brackets##############
# 定义一个递归函数来处理所有的括号
process_module_brackets <- function(
    module_abundance = sub_Sample_KO_pathway,
    module_steps_str = module_steps_str,
    bracket_count = 1,
    step_count = 1,
    module_name = 'Module',
    raw_module_steps = module_steps_str) {

  original_module_steps = raw_module_steps
  # 提取最内层的括号
  brackets <- extract_inner_brackets(module_steps_str)
  # 如果没有找到括号，那么就返回原始字符串
  if (length(brackets) == 0) {
    cat('\n\tStart processing final step...\n\n')
    cat(paste0('\t\tAnalyzing ',module_name,': ',module_steps_str,'\n'))
    sample_module_steps = process_module_step(module_abundance,
                                              module_steps_str,
                                              aggregrate_rowname = module_name,
                                              step_count = bracket_count)[['abundance_table']]
    return((sample_module_steps))
  } else {
    cat(paste0('\n\tNested steps include: ',brackets,'\n\n'))
    cat(paste0('\tStart processing nested steps...\n\n'))
    }
  # 处理每一对括号
  for (bracket in brackets) {
    bracket_name = paste0(module_name, '_', bracket_count)
    cat(paste0('\t\tAnalyzing ',bracket_name,': ',bracket,'\n'))
    module_steps_str <- str_replace(module_steps_str, escape_special_chars(bracket), bracket_name)

    result = process_module_step(module_abundance, bracket, aggregrate_rowname = bracket_name, step_count = 1)
    sample_module_bracket = result[['abundance_table']]
    module_abundance = rbind(module_abundance, sample_module_bracket) %>% unique(.)

    bracket_count = bracket_count + 1
    cat('\n')
  }
  return(process_module_brackets(module_abundance = module_abundance,
                                 module_steps_str = module_steps_str,
                                 bracket_count = bracket_count,
                                 step_count = step_count,
                                 module_name = module_name,
                                 raw_module_steps = raw_module_steps))
}

##############process_all_modules##############
process_all_modules <- function(pathway_infor, Sample_KO) {
  # 初始化一个空的数据框用于存储结果
  result <- data.frame()
  # 循环处理每个模块
  for (each_module in unique(pathway_infor[, 'Module_Entry'])) {
    # 调用process_module_structure处理模块数据
    sub_Sample_KO_pathway <- process_module_structure(pathway_infor, Sample_KO, module = each_module)
    # 如果sub_Sample_KO中有Orthology_Entry
    if (dim(sub_Sample_KO_pathway)[2]>0){
      # 调用process_module_definition处理模块定义
      str <- process_module_definition(sub_Sample_KO_pathway)
      # 调用process_module_brackets处理模块括号结构并将结果添加到result中
      result.tmp <- process_module_brackets(module_abundance = sub_Sample_KO_pathway,
                                            module_steps_str = str, module_name = each_module)
      cat(paste0('\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] Module ',each_module,' finished: ',unique(sub_Sample_KO_pathway$Definition),'\n\n'))
    } else {
      cat(paste0('\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),'] Warning: Orthology Entry of ', each_module,' can not be detected in this KO-Sample file!\n\n'))
      result.tmp = data.frame()
    }
    # 将当前模块的结果追加到总结果中
    result <- rbind(result, result.tmp) %>% unique(.)
  }
  return(result)
}


##############create_module_sample##############
merge_module_name <- function(pathway_infor, module_table) {
  module_infor <- pathway_infor %>%
    select(Module_Entry, Module_Name) %>%
    {unique(.)}
  Module_Sample = module_table %>%
    merge(., module_infor, all.x = T, by = 'Module_Entry') %>%
    {select(., -c(Module_Entry,Orthology_Entry,Definition))} %>%
    {
      rownames(.) = (.$Module_Name)
      (.)
    } %>%
    {select(., -Module_Name)} %>%
    {add_rows_if_not_exists(., add_rows = unique(module_infor$Module_Name))}
  return(Module_Sample)
}

##############create_sub_module_sample##############
create_sub_module_sample <- function(pathway_infor, Module_Sample_scale, out_DIR_Module_Sample_by_pathway, prefix) {
  for (pathway in unique(pathway_infor$Pathway_Name)) {
    pathway_scale <- gsub(' ', '_', pathway)
    out_file <- paste0(out_DIR_Module_Sample_by_pathway, '/', prefix, ".KO_Sample.wide.", pathway_scale, ".tsv")
    each_pathway_infor <- pathway_infor %>%
      {.[(.$Pathway_Name) %in% pathway, c('Module_Name', 'Module_Entry'),drop = F]} %>%
      unique()
    sub_Module_Sample = Module_Sample_scale %>%
      {.[rownames(.) %in% unique(each_pathway_infor$Module_Name),]} %>%
      {rownames_to_column(., var = "Module_Name")} %>%
      {write.table(.,
                  out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}
  }
}

##############process_all_pathways##############
process_all_pathways <- function(pathway_infor, Module_Sample, out_DIR_Module_Sample_by_pathway, prefix, compare_method = c("log", "avg", "round")) {
  if (compare_method == "log") {
    Module_Sample_scale <- Module_Sample %>%
      {round(log10((.) + 1), 4)}
  } else if (compare_method == "avg") {
    Module_Sample_scale <- Module_Sample %>%
      {round(ata_cal(.), 4)}
  } else if (compare_method == "round") {
    Module_Sample_scale <- Module_Sample %>%
      {round(., 4)}
  } else {
    stop("Invalid compare_method. Choose anyone of the 'log', 'avg' or 'round'.")
  }

  out_file = paste0(out_DIR_Module_Sample_by_pathway, '/', prefix, ".KO_Sample.wide.", 'All_Energy_pathways', ".tsv")
  Module_Sample_scale %>%
      {rownames_to_column(., var = "Module_Name")} %>%
      {write.table(.,
                  out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}

  create_sub_module_sample(pathway_infor, Module_Sample_scale, out_DIR_Module_Sample_by_pathway, prefix)
}


process_all_target <- function(in_KO_pathway_info, in_KO_Sample_wide, out_dir_ko_sample_by_pathway,
                        prefix, table_feature = "presence") {
  cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'Input Sample-KO table type:',table_feature,'\n\n')
  cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'Scale method for plus:',plus_scale_method,'\n\n')
  cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'Scale method for comma:',comma_scale_method,'\n\n')
  # “空格”连接的不同KO认为是不同步骤
  # ，号连接的不同KO，可以是sum相加（计算丰度的话，多个同功能KO丰度加和），max最大值（计算通路完整性）
  # + 号连接的不同KO，可以是平均值（数学上的平均值），min最小值（多个亚基的木桶理论），max最大值（多个亚基的丰度最高值，认为其他亚基不完整），
  ##########################      create dir for output      ##########################
  if(! dir.exists(out_dir_ko_sample_by_pathway)){
    dir.create(out_dir_ko_sample_by_pathway, recursive = T)
  }
  ##########################      pathway infor input      ##########################
  # 'MASH_KEGG_Energy_metabolism.20240315.tsv'
  pathway_infor = read_and_process_pathway_infor(in_KO_pathway_info)
  ##########################      Sample-KO depth input      ##########################
  # 'MASH.20230813.test.KO_Sample.wide.tsv' relative abundance * 100,0000
  # 'user.20230828.test.KO_Sample.wide.RA.tsv'
  Sample_KO_abundance = read_and_process_KO_table(in_KO_Sample_wide, pathway_infor)

  ##############    convert presence     ##############
  if (table_feature == "presence") {
    cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'Converting abundance table to presence table...\n\n')
    Sample_KO_abundance = convert_abundance_to_presence(Sample_KO_abundance)
  }

  ##############    Calculating coverage of respective modules     ##############
  module_table_coverage = process_all_modules(pathway_infor, Sample_KO = Sample_KO_abundance)
  Module_Sample_Coverage =  merge_module_name(pathway_infor, module_table = module_table_coverage)

  ##############    output format     ##############
  if (table_feature == "presence") {

    ##############    Output present KOs of respective modules    ##############
    cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'Summarizing present KOs of respective modules...\n\n')
    Module_Sample_KO_list = group_ko_by_module(pathway_infor, Sample_KO_abundance) %>%
      {.[match(rownames(Module_Sample_Coverage), rownames(.)), match(colnames(Module_Sample_Coverage), colnames(.))]}

    out_file = paste0(out_dir_ko_sample_by_pathway, '/', prefix, '.Module_Sample_Presence_KO_list.All_Energy_pathways.tsv')
    Module_Sample_KO_list %>%
        {rownames_to_column(., var = "Module_Name")} %>%
        {write.table(.,
                    out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}

    ##############    Output coverage of respective modules for all pathways    ##############
    out_file = paste0(out_dir_ko_sample_by_pathway, '/', prefix, '.Module_Sample_Presence.All_Energy_pathways.tsv')
    Module_Sample_Coverage %>%
        {rownames_to_column(., var = "Module_Name")} %>%
        {write.table(.,
                    out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}

    ##############    Output coverage of respective modules for each pathway   ##############
    out_dir_ko_sample_by_pathway_presence = paste0(out_dir_ko_sample_by_pathway, '/presence/KO_Sample_by_pathway/')
    if(! dir.exists(out_dir_ko_sample_by_pathway_presence)){
      dir.create(out_dir_ko_sample_by_pathway_presence, recursive = T)}
    Module_Sample_pathway_presence = process_all_pathways(pathway_infor, Module_Sample_Coverage, out_dir_ko_sample_by_pathway_presence, prefix, compare_method = "round")

  } else if (table_feature == "abundance") {

    ##############    Output present KOs of respective modules    ##############
    cat(paste0('[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'Summarizing present KOs of respective modules...\n\n')
    Module_Sample_KO_list = group_ko_by_module(pathway_infor, Sample_KO_abundance) %>%
      {.[match(rownames(Module_Sample_Coverage), rownames(.)), match(colnames(Module_Sample_Coverage), colnames(.))]}

    out_file = paste0(out_dir_ko_sample_by_pathway, '/', prefix, '.Module_Sample_Abundance_KO_list.All_Energy_pathways.tsv')
    Module_Sample_KO_list %>%
        {rownames_to_column(., var = "Module_Name")} %>%
        {write.table(.,
                    out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}

    ##############    Output coverage of respective modules for all pathways    ##############
    out_file = paste0(out_dir_ko_sample_by_pathway, '/', prefix, '.Module_Sample_Abundance.All_Energy_pathways.tsv')
    Module_Sample_Coverage %>%
        {rownames_to_column(., var = "Module_Name")} %>%
        {write.table(.,
                    out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}

    ##############    Output coverage of respective modules for each pathway   ##############
    out_dir_ko_sample_by_pathway_log = paste0(out_dir_ko_sample_by_pathway, '/log_transformed/KO_Sample_by_pathway/')
    if(! dir.exists(out_dir_ko_sample_by_pathway_log)){
      dir.create(out_dir_ko_sample_by_pathway_log, recursive = T)
    }
    out_dir_ko_sample_by_pathway_avg = paste0(out_dir_ko_sample_by_pathway, '/avg_transformed/KO_Sample_by_pathway/')
    if(! dir.exists(out_dir_ko_sample_by_pathway_avg)){
      dir.create(out_dir_ko_sample_by_pathway_avg, recursive = T)
    }
    Module_Sample_pathway_log = process_all_pathways(pathway_infor, Module_Sample_Coverage, out_dir_ko_sample_by_pathway_log, prefix, compare_method = "log")
    Module_Sample_pathway_avg = process_all_pathways(pathway_infor, Module_Sample_Coverage, out_dir_ko_sample_by_pathway_avg, prefix, compare_method = "avg")

  }
  return(cat(paste0('\n[',format(Sys.time(), "%Y-%m-%d %H:%M:%S"),']'),'M1S6.Sample_Module.group_by_pathway.cmd.R finished!\n\n'))
}

plus_scale_method = as.character(plus_scale_method) # mean, max, min
comma_scale_method = as.character(comma_scale_method) # max, sum
table_feature = as.character(table_feature) # abundance, presence"
process_all_target(in_KO_pathway_info, in_KO_Sample_wide, out_dir_ko_sample_by_pathway,
                   prefix, table_feature = table_feature)
