#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例脚本
演示如何使用kraken_analysis_python.py
"""

import os
import subprocess
import pandas as pd

def create_example_data():
    """
    创建示例数据文件用于测试
    """
    # 创建示例的percent.csv文件 (B.P.percent.csv)
    example_percent_data = {
        'taxon': ['Proteobacteria', 'Firmicutes', 'Bacteroidetes', 'Actinobacteria', 'Cyanobacteria'],
        'Sample1': [45.2, 30.1, 15.3, 8.4, 1.0],
        'Sample2': [42.8, 28.5, 18.2, 9.1, 1.4],
        'Sample3': [38.9, 35.2, 16.8, 7.8, 1.3],
        'Sample4': [41.5, 32.4, 17.1, 8.2, 0.8],
        'Sample5': [44.1, 29.8, 16.5, 8.9, 0.7],
        'Sample6': [39.7, 33.1, 18.4, 7.6, 1.2]
    }
    
    percent_df = pd.DataFrame(example_percent_data)
    percent_df.to_csv('B.P.percent.csv', index=False)
    print("Created example B.P.percent.csv")
    
    # 创建示例的group.txt文件
    group_data = {
        'sample_name': ['Sample1', 'Sample2', 'Sample3', 'Sample4', 'Sample5', 'Sample6'],
        'group': ['Group1', 'Group1', 'Group1', 'Group2', 'Group2', 'Group2']
    }
    
    group_df = pd.DataFrame(group_data)
    group_df.to_csv('group.txt', sep='\t', index=False)
    print("Created example group.txt")


def run_analysis():
    """
    运行分析脚本
    """
    # 创建输出目录
    output_dir = 'output_results'
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建命令
    cmd = [
        'python', 'kraken_analysis_python.py',
        '--percent_file', 'B.P.percent.csv',
        '--group_file', 'group.txt',
        '--output_dir', output_dir,
        '--domain', 'B',
        '--taxonomy', 'P',
        '--distance_method', 'braycurtis'
    ]
    
    print("Running analysis...")
    print("Command:", ' '.join(cmd))
    
    # 运行命令
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"Error running analysis: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)


def show_results():
    """
    显示结果文件
    """
    output_dir = 'output_results'
    
    print("\n=== Generated Files ===")
    for file in os.listdir(output_dir):
        if file.endswith('.csv'):
            print(f"\n--- {file} ---")
            df = pd.read_csv(os.path.join(output_dir, file))
            print(f"Shape: {df.shape}")
            print("First few rows:")
            print(df.head())


if __name__ == '__main__':
    print("=== Creating Example Data ===")
    create_example_data()
    
    print("\n=== Running Analysis ===")
    run_analysis()
    
    print("\n=== Showing Results ===")
    show_results()
