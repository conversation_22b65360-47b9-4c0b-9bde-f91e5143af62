#!/bin/bash
:<<!EOF!
 * @Date: 2023-06-14 16:58:11
 * @LastEditors: Liuyang<PERSON><PERSON>
 * @LastEditTime: 2024-03-21 14:35:50
 * @FilePath: /liliuyang/R/Rscript.MASH/202308-v7/M1S5.KO_Sample.group_by_pathway.sh
 * @Description: # M1S5.KO_Sample.group_by_pathway.sh 和 M1S4.KO_Sample.group_by_pathway.sh的区别是改动了 M1S5.KO_Sample.group_by_pathway.cmd.R和M1S5.Heatmap.cmd.R
!EOF!

help_message () {
	echo ""
	echo "Usage: M1S5.KO_Sample.group_by_pathway.sh [options] -i input_file -k KO_pathway_info -s script_DIR -p A001 -o output_DIR"
	echo "Note1: Make sure to provide directory and file prefix."
	echo ""
	echo "Options:"
	echo ""
	echo "	-i STR          input_file"
	echo "	-k STR          file for KO_pathway_info"
	echo "	-s STR          directory for script"
	echo "	-p STR          prefix for each batch of sample list"
	echo "	-o STR          directory for output and error files (if exist). e.g., prefix.KO_Sample.wide.tsv, prefix.error_KO_sum.tsv"
	echo "";}

########################################################################################################
########################               LOADING IN THE PARAMETERS                ########################
########################################################################################################
#
## load in params
OPTS=`getopt -o hi:k:s:p:o: --long input_file:,KO_pathway_info:,script_DIR:,prefix:,output_DIR:,help -- "$@"`
if [ $? -ne 0 ]; then help_message; exit 1; fi
# loop through input params
while true; do
		case "$1" in
				-i) input_file=$2; shift 2;;
				-k) KO_pathway_info=$2; shift 2;;
				-s) script_DIR=$2; shift 2;;
				-p) prefix=$2; shift 2;;
				-o) output_DIR=$2; shift 2;;
				-h | --help) help_message; exit 1; shift 1;;
				--) help_message; exit 1; shift; break;;
				*) break;;
		esac
done

########################################################################################################
########################           MAKING SURE EVERYTHING IS SET UP             ########################
########################################################################################################


#check if the prefix exists
if [ -z $prefix ]; then
	echo "Please input the prefix. Exiting..."
	help_message; exit 1
fi


#check if the input_file dir exists
if [ -z ${input_file} ]; then
	echo "Please input the input_file."
	help_message; exit 1
else
	if [ ! -s $input_file ]; then
			echo "File $input_file does not exist. Exiting..."
			help_message; exit 1
	fi
fi


#check if the KO_pathway_info was input
if [ -z ${KO_pathway_info} ]; then
	echo "Please input the KO pathway info."
	help_message; exit 1
else
	#check if the KO_pathway_info file exists
	if [ ! -s $KO_pathway_info ]; then
			echo "$KO_pathway_info does not exist. Exiting..."
			help_message; exit 1
	fi
fi

# Checks for scripts folder
if [ -z ${script_DIR} ]; then
	echo "Please input the script_DIR."
	help_message; exit 1
else
	if [ ! -s ${script_DIR}/M1S5.KO_Sample.group_by_pathway.cmd.R ]; then
			echo "The file ${script_DIR}/M1S5.KO_Sample.group_by_pathway.cmd.R does not exist."
			help_message; exit 1
	fi
	if [ ! -s ${script_DIR}/M1S5.Heatmap.cmd.R ]; then
			echo "The file ${script_DIR}/M1S5.Heatmap.cmd.R does not exist."
			help_message; exit 1
	fi
fi


#check if the output_DIR dir exists
if [ -z ${output_DIR} ]; then
	echo "Please input the output_DIR."
	help_message; exit 1
fi
########################################################################################################
########################                    BEGIN PIPELINE!                     ########################
########################################################################################################

eval "$(conda shell.bash hook)"
conda activate R4.2

output_KO_Sample_by_pathway=${output_DIR}/KO_Sample_by_pathway/
output_Heatmap_by_pathway=${output_DIR}/Heatmap_by_pathway/

if [ ! -d ${output_DIR} ]; then mkdir -p ${output_DIR};
else
	echo "Warning: ${output_DIR} already exists."
fi


if [ ! -d ${output_DIR}/KO_Sample_by_pathway/ ]; then mkdir -p ${output_DIR}/KO_Sample_by_pathway/;
else
    echo "Warning: ${output_DIR}/KO_Sample_by_pathway/ already exists."
		rm -r ${output_KO_Sample_by_pathway}/${prefix}.KO_Sample.wide.*.tsv
		rm -r ${output_KO_Sample_by_pathway}/${prefix}.KO_Sample.error.*.tsv
fi

if [ ! -d ${output_DIR}/Heatmap_by_pathway/ ]; then mkdir -p ${output_DIR}/Heatmap_by_pathway/;
else
    echo "Warning: ${output_DIR}/Heatmap_by_pathway/ already exists."
		rm -r ${output_Heatmap_by_pathway}/${prefix}.KO_Sample.wide.*.pdf
		rm -r ${output_Heatmap_by_pathway}/${prefix}.KO_Sample.wide.*.png
fi

Rscript ${script_DIR}/M1S5.KO_Sample.group_by_pathway.cmd.R \
		--in_KO_Sample_wide ${input_file} \
		--in_KO_pathway_info ${KO_pathway_info} \
		--prefix ${prefix} \
		--out_DIR_KO_Sample_by_pathway ${output_KO_Sample_by_pathway}

Rscript ${script_DIR}/M1S5.Heatmap.cmd.R \
    --user_Sample_in_DIR ${output_KO_Sample_by_pathway} \
    --out_DIR ${output_Heatmap_by_pathway}

########################################################################################################
########################      GROUP KO AND ABUNDANCE BY PATHWAY PIPELINE SUCCESSFULLY FINISHED!!!         ########################
########################################################################################################
echo "GROUP KO AND ABUNDANCE BY PATHWAY PIPELINE SUCCESSFULLY FINISHED!!!"
