###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2022-11-11 22:58:58
#' @FilePath: /R/Rscript.kraken/kraken.Calculate.R
#' @Description:

suppressMessages(library(GetoptLong))
suppressMessages(library(tidyverse))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Source=s", "Total abundance table, e.g., kraken2_taxonomic_profiles.tsv",
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Abundance=s", "output Dir for Total Relative Abundance Table, e.g., Abundance_Table",
  "Barplot=s", "output Dir for Barplot Table, e.g., Barplot_Table",
  "Heatmap=s", "output Dir for Heatmap Table, e.g., Heatmap_Table",
  "PCoA=s", "output Dir for PCoA Table, e.g., PCoA_Table",
  "verbose!","print messages"
)

suppressMessages(library(reshape2))
suppressMessages(library(vegan))
suppressMessages(library(tidyverse))
suppressMessages(library(ape))
##########################      底层数据自定义计算函数     ##########################
ra_cal = function(data = taxonomic_profiles,
                  domain_level = 'B',
                  tax_level = 'P'){
  #  从总的[物种row-样品col]表格,获取各个分类水平的[样品row-物种col]的相对丰度表格
  # data should be the species-sample table, and this table will be transferred to be a sample-species table with relative abundance
  # domain_level can be any of the 'A', 'B', 'E', 'V'
  # tax_level can be any of the 'P', 'C', 'O', 'F', 'G', 'S'
  # 取分类的子集
  tax_df = data %>%
    {.[.$Domain %in% domain_level,]} %>%
    {.[.$Level %in% tax_level,]}
  rownames(tax_df) = tax_df$Clade_name
  # 保留数值列, 计算相对丰度
  tax_df.t = tax_df %>%
    {subset(., select=-c(get('Domain')))} %>%
    {subset(., select=-c(get('Level')))} %>%
    {subset(., select=-c(get('Clade_name')))} %>%
    {.[rowSums(.)>0,]} %>%
    {.[,colSums(.)>0]} %>%
    {as.data.frame(t(.))} %>%
    {./rowSums(.)}
  return(tax_df.t)
}


tax_cal = function(data = tax_df.t,
                   tax_num = 29,
                   digit_num = 6){
  # 获取主要类群
  # data, 序列丰度表格
  # tax_num, 保留最丰富类群数量
  # digit_num, 相对丰度小数位数
  if (dim(data)[2] < tax_num) {
    need_data = data %>%
      {.[, order(colSums(.),decreasing = T)]} %>%
      {.[,1:dim(data)[2]]} #显示所有
  }
  else  {
    need_data = data %>%
      {.[, order(colSums(.),decreasing = T)]} %>%
      {.[,1:tax_num]} #前29丰度
  }
  Others = 1-rowSums(need_data)
  need_data = cbind(need_data, Others) %>%
    {round(.*100, digit_num)}
  return(need_data)

}

merge_cal = function(data = need_data,
                     group = group
                     ){
  # 以'sample_name'列合并丰度表格和分组信息,并将分组转为因子向量
  # data 丰度表格
  # group 分组信息
  data$sample_name <- rownames(data)
  data_merge = merge(data, group, by='sample_name', all.x = TRUE)
  # name for the sample
  rownames(data_merge)=data_merge$sample_name
  # 按照分组顺序排序
  data_merge_order = data_merge[order(data_merge$group, decreasing = F), ]
  # 赋予分组因子
  levels_group = sort(unique(group$group))
  data_merge_order$group <- factor(data_merge_order$group, levels = levels_group)
  return(data_merge_order)
}


melt_cal = function(data = need_data,
                     group = group,
                     variable.name='Taxonomy',
                     value.name='Abudance'
                     ){
  # wide to long, 以'sample_name'列合并丰度表格和分组信息, 并将数据转为长数据格式
  # data 丰度表格
  # group 分组信息
  colname_data = colnames(data)
  plotdata_group = merge_cal(data, group) %>%
    melt(.,id.vars=c('sample_name','group'),measure.vars=colname_data,
                      variable.name=variable.name,
                      value.name=value.name)
  return(plotdata_group)
}
##########################      Data input      ##########################
taxonomic_profiles <- read.csv(Source, sep = '\t', quote='')
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1)
levels_group = sort(unique(group$group))
group$group=factor(group$group, levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列
################################## for循环跑每个类别 ##################################
for (variable in c(Abundance,Barplot,Heatmap,PCoA)) {
  if(! dir.exists(variable)){
    dir.create(variable)
  }
}

for (domain in c('A', 'B', 'E', 'V')) {
  print(paste0('Domain: ',domain))
  for (taxonomy in c('P', 'C', 'O', 'F', 'G', 'S')) {
    print(paste0('Taxonomy: ',taxonomy))
    ra_tax = ra_cal(taxonomic_profiles, domain, taxonomy)
    # output the 24 tables
    ra_tax %>%
      {round(.*100, 6)} %>%
      {as.data.frame(t(.))} %>%
      {rownames_to_column(., var='taxon')} %>%
      {write.table(.,
                   paste0(Abundance, "/", domain, ".", taxonomy, ".percent.csv"),
                   sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
    # output the 24 long tables with group information for barplot
    # ra_tax %>%
    #   {tax_cal(., 29, 4)} %>%
    #   {melt_cal(., group, 'Taxonomy', 'Abudance')} %>%
    #   {write.table(.,
    #                paste0(Barplot, "/", "Barplot.", domain, ".", taxonomy, ".csv"),
    #                sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
    barplot_table = ra_tax %>% {tax_cal(., 29, 4)}
    barplot_table %>% 
      {data.frame(
        Tax = colnames(.) #,
        #MeanAbundance = colSums(.)/dim(.)[1]
      )} %>% 
      {write.table(.,
                   paste0( Barplot,"/", "Barlegend.", domain, ".", taxonomy, ".csv"),
                   sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
    barplot_table %>% 
      {melt_cal(., group, 'Taxonomy', 'Abudance')} %>%
      {write.table(.,
                   paste0(Barplot, "/", "Barplot.", domain, ".", taxonomy, ".csv"),
                   sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
  }

  for (beta in c('bray', 'jaccard')) {
    print(paste0('Method: ',beta))
    dist_bray = ra_cal(taxonomic_profiles, domain, 'S') %>%
      vegdist(., method = beta, na.rm = TRUE) %>%
      as.matrix(as.dist(.))
    # output the 8 distance tables for heatmap
    dist_bray %>%
      {round(., 6)} %>%
      {as.data.frame(.)} %>%
      {rownames_to_column(., var='sample_name')} %>%
      {write.table(.,
                   paste0(Heatmap, "/", "Heatmap.", domain, ".", beta, ".csv"),
                   sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
    ## PCoA所需数据
    df.pcoa = pcoa(dist_bray,correction = "cailliez")

    {data.frame(
    x_label = round(df.pcoa$values$Rel_corr_eig[1]*100,1),
    y_label = round(df.pcoa$values$Rel_corr_eig[2]*100,1)
    )
      } %>%
      {write.table(.,
                   paste0(PCoA, "/", "PCoA.label.", domain, ".", beta, ".csv"),
                   sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
    df.pcoa %>%
      {data.frame(.$vectors)} %>%
        {merge_cal(., group)} %>%
        {.[colnames(.) %in% c('sample_name','group','Axis.1','Axis.2')]} %>%
        {write.table(.,
                     paste0(PCoA, "/", "PCoA.", domain, ".", beta, ".csv"),
                     sep = ",", row.names = F,  quote = FALSE,  fileEncoding="UTF-8")}
  }
}
