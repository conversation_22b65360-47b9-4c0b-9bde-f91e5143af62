###
#' @Date: 2023-08-18 20:24:02
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-28 11:26:48
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v4/M0S2.Merge_multi_Sample_KO_depth.cmd.R
#' @Description:
###

suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("data.table")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "user_Sample_in_DIR=s", "user_Sample directory",
  "out_DIR=s", "output directory",
  "prefix=s", "prefix",
  "verbose!","print messages"
)

# 'MASH/M1S2_output/MASH.20230818.test.KO_Sample.wide.tsv'
#result <- data.table(read.delim(MASH_Sample_in_file,quote='',sep = '\t',header = T, check.names = FALSE))
count = 1
user_file <- dir(user_Sample_in_DIR)
for (file in user_file) {
  print(file)
  user.tmp <- data.table(read.delim(paste(user_Sample_in_DIR, file, sep = '/'),
                               quote='',sep = '\t',header = T, check.names = FALSE))
  setnames(user.tmp, 1, 'KO', skip_absent =T)
  if (count == 1){
    result = user.tmp
  } else {
    result = merge.data.table(result, user.tmp, all = T, by = 'KO')
  }
  count = count + 1
}

result[is.na(result)] <- 0

if(! dir.exists(out_DIR)){
  dir.create(out_DIR, recursive = T)
}

write.table(result, paste0(out_DIR, '/', prefix, '.KO_Sample.wide.tsv'), row.names = FALSE, col.names = T,
            sep = '\t', quote = FALSE, fileEncoding="UTF-8")

print("M0S2.Merge_multi_Sample_KO_depth.cmd.R finished!")
