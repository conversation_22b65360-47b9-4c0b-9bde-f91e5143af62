###
#' @Date: 2022-10-24 13:57:01
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-16 20:49:03
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M2S1.Sample_KO_depth.cmd.R
#' @Description:
###
suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("reshape2","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_user_gene_orf_KO=s", "eggnog ko and contig-cds name",
  "in_MASH_Sample_KO_depth=s", "eggnog ko, contig-cds name, depth column",
  "out_DIR_user_Sample_orf_per_KO=s", "output directory",
  "out_DIR_MASH_Sample_per_KO=s", "output directory",
  "out_DIR_user_error=s", "output error directory",
  "out_DIR_MASH_error=s", "output error directory",
  "prefix=s", "prefix",
  "verbose!","print messages"
)


multi_KO_to_one = function(data = cek.orfX){
  KO_str = data %>%
    {.[, 'KO']} %>%
    {unlist(strsplit(., ","))}
  result = cbind(data$orf, KO_str)
  #colnames(result) = c('orf','KO')
  return(result)
}

seperate_emapper_ko = function(data = contig1000_emapper_ko){
  cekd.m = data %>% {.[c(grep(pattern=",", .$KO)),]}
  cekd.s = data %>% {.[!(.$Sample_orf %in% cekd.m$Sample_orf),]}

  if (length(cekd.m$KO)>0){
    cekd.trans = data.frame()
    for (Sample_orf in cekd.m$Sample_orf) {
      cekd.orfX = cekd.m %>%
        {.[(.$Sample_orf %in% Sample_orf),,drop=F]} %>%
        {multi_KO_to_one(.)}
      cekd.trans = rbind(cekd.trans, cekd.orfX)
    }

    colnames(cekd.trans) = c('Sample_orf','KO')

    if (length(cekd.s$KO)>0){
      result = rbind(cekd.trans, cekd.s)
    } else {
      result = cekd.trans
    }

  } else {
    result = cekd.s
  }
  return(result)
}

# 1 user
user_gene_emapper_ko = in_user_gene_orf_KO %>%
  read.csv(., sep = '\t', quote='', header = F) %>%
  {.[, 1:2]}
colnames(user_gene_emapper_ko) = c('Sample_orf','KO')
user_gene_emapper_ko =user_gene_emapper_ko %>%
  {.[!(.$KO %in% '-'),]}

if (length(user_gene_emapper_ko$KO)>0){

  user_Sample_orf_KO = user_gene_emapper_ko %>%
    {seperate_emapper_ko(.)} %>%
    mutate(.,KO = gsub('ko:','', .$KO))

  for (KO in unique(user_Sample_orf_KO$KO)) {
    user_Sample_orf_per_KO = user_Sample_orf_KO %>%
      {.[(.$KO %in% KO),'Sample_orf',drop=F]} %>%
      {unique(.)}
    user_Sample_orf_per_KO %>%
      {write.table(., paste0(out_DIR_user_Sample_orf_per_KO, '/', prefix, '.user_Sample_orf.', KO, '.tsv'), row.names = FALSE, col.names = FALSE,
                  sep = '\t', quote = FALSE, fileEncoding="UTF-8")}
  }

  # 2 MASH
  MASH_contig1000_emapper_KO_depth = in_MASH_Sample_KO_depth %>%
    read.csv(., sep = '\t', quote='', header = F) %>%
    {.[, 1:2]}
  colnames(MASH_contig1000_emapper_KO_depth) = c('Sample_KO', 'Avg_fold')


  if (length(MASH_contig1000_emapper_KO_depth$Sample_KO)>0){

    MASH_Sample_KO = MASH_contig1000_emapper_KO_depth %>%
      mutate(.,
            Sample = sapply(as.character(.$Sample_KO),
                            function(x) unlist(strsplit(x, "---"))[1]),
            KO = sapply(as.character(.$Sample_KO),
                            function(x) unlist(strsplit(x, "---"))[2])
      ) %>%
      {select(., c('Sample', 'KO', 'Avg_fold'))} %>%
      {unique(.)} %>%
      {.[(.$KO %in% unique(user_Sample_orf_KO$KO)),,drop=F]}

    for (KO in unique(MASH_Sample_KO$KO)) {
      MASH_Sample_per_KO = MASH_Sample_KO %>%
        {.[(.$KO %in% KO),c('Sample','Avg_fold'),drop=F]} %>%
        {unique(.)}
      MASH_Sample_per_KO %>%
        {write.table(., paste0(out_DIR_MASH_Sample_per_KO, '/', prefix, '.MASH_Sample.', KO, '.tsv'), row.names = FALSE, col.names = FALSE,
                    sep = '\t', quote = FALSE, fileEncoding="UTF-8")}
    }

  } else {

    file.create(paste0(out_DIR_MASH_error, '/', prefix, '.error.tsv'))

  }

} else {

  file.create(paste0(out_DIR_user_error, '/', prefix, '.error.tsv'))

}
print("M2S1.Sample_KO_depth.cmd.R finished!")
