###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-16 21:44:15
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M1S4.Heatmap.R
#' @Description:

rm(list = ls(all.names = TRUE))
suppressMessages(library(pheatmap))
suppressMessages(library(dplyr))
suppressMessages(library(ggplot2))

setwd('/data1/liliuyang/MASH-OCEAN/20230721/test/user/M1S4_output/KO_Sample_by_pathway')
##########################      group input      ##########################
group = read.csv('/data1/liliuyang/MASH-OCEAN/20230721/test/MASH/AAA_reference/group.tsv',sep='\t',header = TRUE,row.names = 1)
levels_group = sort(unique(group$group))
group$group=factor(group$group,
                            levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列
## 热图
anno_row = group %>%
  {subset(., select=get('group'))}
ann_colors = list(
  group = c(GroupA = "#A3A500", GroupB = "#00BF7D", GroupC = "#00B0F6", GroupD = "#E76BF3", GroupE = "#F8766D")
)
##########################      Sample-KO depth input      ##########################
sub_Sample_KO = read.csv('MASH.20230813.test.KO_Sample.wide.Photosynthesis.tsv', sep='\t',header = TRUE, row.names = 1)

## 热图
sub_Sample_KO %>%
  {log10(.+1)} %>%
  {pheatmap(.,
            method = 'average',
            #cutree_rows = length(unique(group$group)),
            cutree_cols = length(unique(group$group)),
            na_col='grey',
            cluster_rows = F,
            cluster_cols = T,###是否对行列聚类
            show_rownames = T,show_colnames = T,###是否显示行名列名
            cellwidth = 7,cellheight = 6,###固定每个cell的宽和高
            display_numbers = F,####是否显示每个单元格的值
            fontsize_row = 6,
            fontsize_col	= 5,
            angle_col = 45,
            annotation_col = anno_row,
            annotation_colors = ann_colors
  )} %>%
{ggsave(paste0('MASH.20230813.test.KO_Sample.wide.Photosynthesis.png'),
        ., width = 14, height = 20)
}
