###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-03-20 20:52:01
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v7/M1S4.Heatmap.cmd.R
#' @Description: 删除分组信息

suppressMessages(library(GetoptLong))
suppressMessages(library(pheatmap))
suppressMessages(library(tidyverse))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "user_Sample_in_DIR=s", "user_Sample directory",
  "out_DIR=s", "output directory for Heatmap figure",
  "verbose!","print messages"
)

draw_heatmap <- function(sub_Sample_KO) {
    #  'sub_Sample_KO' 是热图数据
  tryCatch({
    p <- pheatmap(sub_Sample_KO,
                  method = 'average',
                  na_col = 'grey',
                  cluster_rows = F,
                  cluster_cols = T,###是否对行列聚类
                  show_rownames = T,show_colnames = T,###是否显示行名列名
                  cellwidth = 6,cellheight = 6,###固定每个cell的宽和高
                  display_numbers = F,####是否显示每个单元格的值
                  fontsize_row = 5,
                  fontsize_col = 5,
                  angle_col = 45,
                  silent = TRUE
    )
    return(p)
  }, error = function(e) {
    message("Error in pheatmap: ", e)
    min_val <- min(sub_Sample_KO)
    max_val <- max(sub_Sample_KO,1e-10)
    # 创建 breaks 向量，其中的值从最小值到最大值均匀分布，且0对应于白色
    breaks <- c(min_val, max_val)
    colors <- rep("blue", times = 100)
    p <- pheatmap(sub_Sample_KO,
                  na_col = 'grey',
                  scale = 'none',
                  breaks = breaks,
                  cluster_rows = F,
                  cluster_cols = F,###当出现错误时，不进行列聚类
                  show_rownames = T,show_colnames = T,###是否显示行名列名
                  cellwidth = 6,cellheight = 6,###固定每个cell的宽和高
                  display_numbers = F,####是否显示每个单元格的值
                  fontsize_row = 5,
                  fontsize_col = 5,
                  angle_col = 45,
                  silent = TRUE
    )
    return(p)
  })
}


if(! dir.exists(out_DIR)){
  dir.create(out_DIR)
}

user_file <- dir(user_Sample_in_DIR)
for (file in user_file) {
  if (grepl(".KO_Sample.wide.", file) & grepl(".tsv", file)){
    file_name = gsub(".tsv", "", file)
    print(file)
    sub_Sample_KO <- read.csv(paste(user_Sample_in_DIR, file, sep = '/'),
                                quote='',sep = '\t',header = T, row.names = 1, check.names = FALSE)

    num_sample = length(unique(colnames(sub_Sample_KO)))
    print(paste0("num of sample: ", num_sample))
    # 按照线性关系width=3+0.5*num_group
    figure_width=7+0.08*num_sample

    num_KO = length(unique(rownames(sub_Sample_KO)))
    print(paste0("num of KO: ", num_KO))
    # 按照线性关系width=3+0.5*num_group
    figure_height=5+0.08*num_KO
    ## 热图
    p =  draw_heatmap(sub_Sample_KO)
    ggsave(paste0(file_name, ".png"),
                  path = out_DIR, p,
                  width = figure_width, height = figure_height, limitsize = FALSE)
    ggsave(paste0(file_name, ".pdf"),
                  path = out_DIR, p,
                  width = figure_width, height = figure_height, limitsize = FALSE)
  }
}

print("M1S4.Heatmap.cmd.R finished!")
