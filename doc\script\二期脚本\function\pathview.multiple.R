#!/usr/bin/env Rscript
###
#' @Author: LiQiang
#' @Date: 2023-08-13
#' @LastEditors: LiQiang
#' @LastEditTime: 2023-08-17
#' @FilePath: /bdp-picb/bioinfo/liqiang/project/metagenome/mash-ocean/pathview/scripts/pathview.R
#' @Description: 
###
library(pathview)
data("demo.paths")

if (!suppressWarnings(suppressMessages(require("optparse", character.only = TRUE, quietly = TRUE, warn.conflicts = FALSE)))) {
    install.packages("optparse", repos=site)
    require("optparse",character.only=T)
}

if (TRUE){
  option_list = list(
    make_option(c("-k", "--ko_file"), type="character", default="demo.multi.KO_depth.tsv",
                help="ko abundance file [default %default]"),
    make_option(c("-o", "--prefix"), type="character", default="demo.multi",  # user define or tase_id
                help="output file prefix [default %default]"),
    make_option(c("-p", "--pathway"), type="character", default="cns",
                help="pathway id to plot [default %default]"),
     make_option(c("-d", "--outdir"), type="character",
                help="outdir to save [default %default]")
  )
  opts = parse_args(OptionParser(option_list=option_list))
}



out_path<-opts$outdir
setwd(out_path)
#setwd("/bdp-picb/bioinfo/liqiang/project/metagenome/mash-ocean/pathview/test-final/")
#ko_file = "/bdp-picb/bioinfo/liqiang/project/metagenome/mash-ocean/pathview/test/MASH.20230614.sub.KO_Sample.3.tsv"
ko_abund <- read.table(opts$ko_file, header=T, row.names = 1,sep="\t")
ko_abund_trans <- log10(ko_abund+1)
# odds ration mean, then log trans
ko_abund_trans2_tmp <- ko_abund
for (i in 1:length(rownames(ko_abund))) {
    ko_abund_trans2_tmp[i,] = (ko_abund[i,]+1)/(mean(t(ko_abund[i,]))+1)

}
ko_abund_trans2 <- log2(ko_abund_trans2_tmp)
out_suffix_log = paste0(opts$prefix,".log")  
out_suffix_fold_change = paste0(opts$prefix,".fc.log")

if(opts$pathway == "cns"){
top = ceiling(max(ko_abund_trans))
for (path_id in c("00190","00195","00196","00680","00710","00720","00910","00920")){
        pv.out <- pathview(gene.data = ko_abund_trans,   ### gene.data = gse16873.d[, 1:3] ### multiple samples
                   cpd.data = NULL,
                   pathway.id = path_id,
                   species = "ko",
                   out.suffix = out_suffix_log,
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene = top,cpd = 1),
                   both.dirs = list(gene = F, cpd = T),kegg.native = TRUE)
}




#top = ceiling(max(ko_abund_trans1))
top = 2
for (path_id in c("00190","00195","00196","00680","00710","00720","00910","00920")){
        pv.out <- pathview(gene.data = ko_abund_trans2,   ### gene.data = gse16873.d[, 1:3] ### multiple samples
                   cpd.data = NULL,
                   pathway.id = path_id,
                   species = "ko",
                   out.suffix = out_suffix_fold_change,
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene = top,cpd = 1),
                   both.dirs = list(gene = T, cpd = T),kegg.native = TRUE)
}
}else{
      pv.out <- pathview(gene.data = ko_abund_trans,   ### gene.data = gse16873.d[, 1:3] ### multiple samples
                   cpd.data = NULL,
                   pathway.id = opts$pathway,
                   species = "ko",
                   out.suffix = out_suffix_log,
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene = top,cpd = 1),
                   both.dirs = list(gene = F, cpd = T),kegg.native = TRUE)
                   
        pv.out <- pathview(gene.data = ko_abund_trans2,   ### gene.data = gse16873.d[, 1:3] ### multiple samples
                   cpd.data = NULL,
                   pathway.id = opts$pathway,
                   species = "ko",
                   out.suffix = out_suffix_fold_change,
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene = top,cpd = 1),
                   both.dirs = list(gene = T, cpd = T),kegg.native = TRUE)  

}




###  multiple samples   ###
#Task_id = "";
#pv.out <- pathview(gene.data = gse16873.d[, 1:3]
#                   cpd.data = NULL,
#                   pathway.id = path_id,
#                   species = "ko",
#                   out.suffix = Task_id,
#                   discrete = list(gene = T,cpd = F),
#                   bins = list(gene = 1,cpd = 10),
#                   limit = list(gene = 1, cpd = 1),both.dirs = list(gene = F, cpd = T),kegg.native = TRUE)
#}

