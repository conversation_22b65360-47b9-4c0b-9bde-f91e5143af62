###
#' @Date: 2022-10-24 13:57:01
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-17 21:41:35
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M2S2.Sample_map.cmd.R
#' @Description:
###
suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("reshape2","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_user_KO_Sample=s", "the tsv file of Sample and KO depth based on user KO",
  "in_MASH_metadata=s", "the overall MASH metadat file",
  "out_MASH_Sample_depth_per_KO=s", "output file path",
  "verbose!","print messages"
)

# 1 user selected sample from MASH
user_MASH_KO_Sample = in_user_KO_Sample %>%
  read.csv(., sep = '\t', quote='', header = F) %>%
  {.[, 1:2]}
colnames(user_MASH_KO_Sample) = c('Sample','Avg_fold')

# 2 MASH metadata
MASH_metadata = in_MASH_metadata %>%
  read.csv(., sep = '\t', quote='', header = T, check.names = F) %>%
  mutate(., Sample = (.[, 'Run ID']))

# 3 merge 2 files to a tsv file
user_MASH_metadata = MASH_metadata %>%
  merge(., user_MASH_KO_Sample, by = 'Sample', all.y = T) %>%
  {mutate(., Avg_fold_log10 = round(log10(.$Avg_fold+1), 4))} %>%
  select(., -c(Sample, Avg_fold))
print(head(user_MASH_metadata))
user_MASH_metadata  %>%
  {write.table(., out_MASH_Sample_depth_per_KO, row.names = FALSE, col.names = T,
              sep = '\t', quote = FALSE, fileEncoding="UTF-8")}


print("M2S2.Sample_map.cmd.R finished!")
