import sys
import getopt
import pandas as pd


def lefse_format(abundancefile, metadatafile, group, outputfile):
    abundance_frequency = pd.read_table(abundancefile, header=1, index_col=0)
    metadata = pd.read_table(metadatafile, index_col=0)

    abundance_frequency.columns = abundance_frequency.columns.map(lambda x: metadata[group][x])

    filename = abundancefile.split('/')[-1]

    if 'collapse_frequency_table.tsv' in filename:
        abundance_frequency.index = abundance_frequency.index.map(lambda x: x.replace(';', '|'))
    if 'ec' in filename or 'ko' in filename or 'pathway' in filename:
        abundance_frequency = (abundance_frequency - abundance_frequency.mean()) / abundance_frequency.std()

    abundance_frequency.to_csv(outputfile, sep='\t')


def main(argv):
    try:
        opts, args = getopt.getopt(argv, "hi:m:c:o:",
                                   ["abundancefile=", "metadatafile=", "group=", "outputfile="])
    except getopt.GetoptError:
        print('''lefse_format.py 
              -i <abundance file> 
              -m <metadata file>
              -c <group column>
              -o <outputfile>
              ''')
        sys.exit(2)
    for opt, arg in opts:
        if opt == '-h':
            print('''lefse_format.py 
              -i <abundance file> 
              -m <metadata file>
              -c <group column>
              -o <outputfile>''')
            sys.exit()
        elif opt in ("-i", "--abundancefile"):
            abundancefile = arg
        elif opt in ("-m", "--metadatafile"):
            metadatafile = arg
        elif opt in ("-c", "--group"):
            group = arg
        elif opt in ("-o", "--outputfile"):
            outputfile = arg
    print('输入的丰度文件为：', abundancefile)
    print('输入的meta文件为：', metadatafile)
    print('输入的分组标签为：', group)
    print('输出的文件为：', outputfile)
    lefse_format(abundancefile, metadatafile, group, outputfile)


if __name__ == '__main__':
    main(sys.argv[1:])

