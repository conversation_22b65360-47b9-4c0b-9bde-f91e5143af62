###
#' @Date: 2023-08-18 22:02:05
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-30 13:44:07
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v4/M0S1.Merge_orf_KO_depth.cmd.R
#' @Description:
###


suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("data.table","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_orf_KO=s", "orf_KO mapping file",
  "depth_bbmap_in_file=s", "orf depth_bbmap file",
  "sample_name=s", "file store the sample name",
  "KO_list_in_file=s", "file of KO_list",
  "out_DIR=s", "output directory",
  "verbose!","print messages"
)

# KO list
KO_list = KO_list_in_file %>%
  read.csv(., sep = '\t', quote='', header = F) %>%
  {.[, 1]}

# sample name
sample = sample_name %>%
  read.csv(., sep = '\t', quote='', header = F) %>%
  {.[, 1]}

# 'MASH/M1S2_output/MASH.20230818.test.KO_Sample.wide.tsv'
contig1000_depth <- data.table(read.csv(depth_bbmap_in_file, sep = '\t',
                    quote='', header = T, col.names = c('Contig','Avg_fold')))

contig1000_emapper_KO_depth = in_orf_KO %>%
  read.csv(., sep = '\t', quote='', header = T, col.names = c('orf','KO')) %>%
  {.[(.$KO %in% KO_list),]}

if(! dir.exists(out_DIR)){
  dir.create(out_DIR, recursive = T)
}

sample_error = paste0(out_DIR, '/', sample, '.error.tsv')
mapping_error = paste0(out_DIR, '/', sample, '.mapping_error.tsv')
sample_KO_depth = paste0(out_DIR, '/', sample, '.KO_depth.tsv')

if(file.exists(sample_error)){
  file.remove(sample_error)
}

if(file.exists(mapping_error)){
  file.remove(mapping_error)
}

if(file.exists(sample_KO_depth)){
  file.remove(sample_KO_depth)
}

if (length(contig1000_emapper_KO_depth$KO)>0){
  length_insect_orf = contig1000_emapper_KO_depth$orf %>%
    {length(.[(.) %in% contig1000_depth$Contig])}
  if (length(contig1000_emapper_KO_depth$orf) == length_insect_orf){
    print("The Gene Abundance file is orf abundance!")
    colnames(contig1000_depth) = c('orf','Avg_fold')
    contig1000_emapper_KO_depth = contig1000_emapper_KO_depth %>%
      {data.table(.)} %>%
      merge.data.table(., contig1000_depth, all.x = T, by = 'orf') %>%
      select(., c('KO','Avg_fold')) %>%
      {data.frame(.)}
  } else {
    contig1000_emapper_KO_depth = contig1000_emapper_KO_depth %>%
      {mutate(., Contig = sapply(as.character(.$orf),
                                function(x) unlist(strsplit(x, "_\\d*$"))[1]))}
    length_insect_contig = contig1000_emapper_KO_depth$Contig %>%
      {length(.[(.) %in% contig1000_depth$Contig])}
    if (length(contig1000_emapper_KO_depth$Contig) == length_insect_contig){
      print("The Gene Abundance file is contig mean abundance!")
      contig1000_emapper_KO_depth = contig1000_emapper_KO_depth %>%
        {data.table(.)} %>%
        merge.data.table(., contig1000_depth, all.x = T, by = 'Contig') %>%
        select(., c('KO','Avg_fold')) %>%
        {data.frame(.)}
    } else {
      file.create(mapping_error)
      stop("Please check the mapping relationship between the Gene ID file and Gene Abundance file!")
    }
  }

  KO_depth = contig1000_emapper_KO_depth %>%
    {aggregate(
      x=list(Avg_fold = (.[,"Avg_fold"])),
      by=list(KO = (.[,"KO"])
      ),
      FUN=sum
    )} %>%
    mutate(., Avg_fold = round(as.numeric(.$Avg_fold), 4))
  colnames(KO_depth) = c('KO', sample)
  KO_depth %>%
    {write.table(., sample_KO_depth, row.names = FALSE, col.names = T,
                 sep = '\t', quote = FALSE, fileEncoding="UTF-8")}

} else {

  file.create(sample_error)

}





print("M0S1.Merge_orf_KO_depth.cmd.R finished!")
