#! /bin/bash
path="/data/mash"
group_column="group"
scripts_path="/data/mash/script/lefse/scripts"
images_path="/data/mash/script/lefse/images"

input_file=$1 
output_file=$2
metadata=$3

set -e
source /opt/miniconda3/etc/profile.d/conda.sh
conda activate python3.7.4
# 可能需要修改调用脚本lefse_format.py的位置及挂载目录 /bdp-picb/hpcimage/16S_scripts/lefse_format.py
# singularity run -B ${path}:${path} -B ${scripts_path}:/scripts ${images_path}/conda-scipy.sif \
python ${scripts_path}/diff_scripts/lefse_format.py \
-i ${input_file} \
-m ${metadata} \
-c ${group_column} \
-o ${output_file}.txt