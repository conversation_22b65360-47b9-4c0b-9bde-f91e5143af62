#! /bin/bash
scripts_path=$1
inputPath=$2
outputPath=$3

# 根据环境切换是使用miniconda还是anaconda，基础镜像dev.biosino.org/genome/jdk8-python3-singularity3.10.0-miniconda-r4.1:1.0是使用miniconda
# source /opt/conda/etc/profile.d/conda.sh
#source ~/miniconda3/etc/profile.d/conda.sh
# source ~/anaconda3/etc/profile.d/conda.sh
# source activate R4.1
#conda activate R4.1

Rscript ${scripts_path}/kraken.Calculate.cmd.R --Source ${outputPath}/kraken2_taxonomic_profiles.tsv --Group ${inputPath}/group.txt --Abundance ${outputPath}/Abundance_Table --Barplot ${outputPath}/Barplot_Table --Heatmap ${outputPath}/Heatmap_Table --PCoA ${outputPath}/PCoA_Table --Beta_level S

Rscript ${scripts_path}/kraken.Barplot.cmd.R --Input ${outputPath}/Barplot_Table --Output ${outputPath}/Barplot_Figure

# 删除多生成的*_files,节省磁盘
rm -rf ${outputPath}/Barplot_Figure/*_files

Rscript ${scripts_path}/kraken.Heatmap.cmd.R --Group ${inputPath}/group.txt --Group_color ${scripts_path}/group_color.tsv --Input ${outputPath}/Heatmap_Table --Output ${outputPath}/Heatmap_Figure

Rscript ${scripts_path}/kraken.PCoA.cmd.R --Group ${inputPath}/group.txt --Group_color ${scripts_path}/group_color.tsv --Input ${outputPath}/PCoA_Table --Output ${outputPath}/PCoA_Figure
