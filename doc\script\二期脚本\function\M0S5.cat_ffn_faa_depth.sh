#!/bin/bash
:<<!EOF!
 * @Date: 2023-06-14 16:58:11
 * @LastEditors: LiuyangLi
 * @LastEditTime: 2023-09-07 16:18:17
 * @FilePath: /liliuyang/R/Rscript.MASH/202308-v5/M0S5.cat_ffn_faa_depth.sh
 * @Description:
!EOF!

help_message () {
	echo ""
	echo "Usage: M0S5.cat_ffn_faa_depth.sh [options] -i input_DIR -o output_DIR --faa --ffn --depth_bbmap"
	echo "Note1: Make sure to provide input and output directory"
	echo ""
	echo "Options:"
	echo ""
	echo "	-i STR          directory for input gene faa or ffn or depth files, depending on the switch of --faa or --ffn or --depth_bbmap"
	echo "	-o STR          directory for output cat files (if exist)"
	echo "	--faa           user input faa sequence"
	echo "	--ffn           user input ffn sequence"
	echo "	--depth_bbmap   user input depth_bbmap tsv"
	echo "";}

########################################################################################################
########################               LOADING IN THE PARAMETERS                ########################
########################################################################################################
#
# long options defaults
faa=false; ffn=false; depth_bbmap=false;

## load in params
OPTS=`getopt -o hi:o: --long input_DIR:,output_DIR:,faa,ffn,depth_bbmap,help -- "$@"`
if [ $? -ne 0 ]; then help_message; exit 1; fi
# loop through input params
while true; do
		case "$1" in
				-i) input_DIR=$2; shift 2;;
				-o) output_DIR=$2; shift 2;;
				--faa) faa=true; shift 1;;
				--ffn) ffn=true; shift 1;;
				--depth_bbmap) depth_bbmap=true; shift 1;;
				-h | --help) help_message; exit 1; shift 1;;
				--) help_message; exit 1; shift; break;;
				*) break;;
		esac
done

########################################################################################################
########################           MAKING SURE EVERYTHING IS SET UP             ########################
########################################################################################################

# Make sure one of the two method was chosen
if [ $faa = false ] && [ $ffn = false ] && [ $depth_bbmap = false ]; then
	echo "You must select at least one method: --faa or --ffn or --depth_bbmap"
	help_message; exit 1
fi

#check if user emapper annotation file exists
if [ -z ${input_DIR} ]; then
	echo "Please input user emapper annotation file."
	help_message; exit 1
else
	if [ ! -d $input_DIR ]; then
		echo "Error: directory $input_DIR does not exist. Exiting..."
		help_message; exit 1
	fi
fi

#check if the output_DIR dir exists
if [ -z ${output_DIR} ]; then
	echo "Please input the output_DIR."
	help_message; exit 1
fi

########################################################################################################
########################                    BEGIN PIPELINE!                     ########################
########################################################################################################

if [ ! -d ${output_DIR} ]; then mkdir -p ${output_DIR};
else
	echo "Warning: ${output_DIR} already exists."
	rm -r ${output_DIR}/user_faa.cat.faa
	rm -r ${output_DIR}/user_ffn.cat.ffn
	rm -r ${output_DIR}/user_depth_bbmap.cat.txt
fi

########################################################################################################
########################                   RUNNING faa                     ########################
########################################################################################################
# 如果输入 faa
# 默认所有输入文件夹内的文件均为 统一且正确的格式
if [ $faa = true ]; then
	for file in `ls ${input_DIR}/*.faa`
	do
			echo "合并 faa 格式的序列文件 ${file}..."
			sample_name=$(echo ${file} | sed -e "s|${input_DIR}/||g" -e "s|\.faa||g")
			sed "s|>|>${sample_name}---|g" ${file} >> ${output_DIR}/user_faa.cat.faa
	done
fi

########################################################################################################
#########################                   RUNNING ffn                     ##########################
########################################################################################################
# 如果输入 ffn
# 默认所有输入文件夹内的文件均为 统一且正确的格式
if [ $ffn = true ]; then
	for file in `ls ${input_DIR}/*.ffn`
	do
			echo "合并 ffn 格式的序列文件 ${file}..."
			sample_name=$(echo ${file} | sed -e "s|${input_DIR}/||g" -e "s|\.ffn||g")
			sed "s|>|>${sample_name}---|g" ${file} >> ${output_DIR}/user_ffn.cat.ffn
	done
fi

########################################################################################################
#########################                   RUNNING depth_bbmap                     ##########################
########################################################################################################
# 如果输入 depth_bbmap
# 默认所有输入文件夹内的文件均为 统一且正确的格式
if [ $depth_bbmap = true ]; then
	# 生成 depth_bbmap 总文件的表头
	echo -e "Contig\tAvg_fold" >> ${output_DIR}/user_depth_bbmap.cat.txt
	for file in `ls ${input_DIR}/*_depth_bbmap.txt`
	do
			echo "合并丰度文件 ${file}..."
			sample_name=$(echo ${file} | sed -e "s|${input_DIR}/||g" -e "s|_depth_bbmap\.txt||g")
			sed -e "1d" -e "s|^|${sample_name}---|g" ${file} >> ${output_DIR}/user_depth_bbmap.cat.txt
	done
fi

########################################################################################################
########################            M0S5 SUCCESSFULLY FINISHED!!!               ########################
########################################################################################################
echo "M0S5 PIPELINE SUCCESSFULLY FINISHED!!!"
