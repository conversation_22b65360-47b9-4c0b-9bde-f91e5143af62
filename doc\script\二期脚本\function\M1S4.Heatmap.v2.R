###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-30 16:37:04
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v4/M1S4.Heatmap.v2.R
#' @Description: 删除分组信息

rm(list = ls(all.names = TRUE))
suppressMessages(library(pheatmap))
suppressMessages(library(dplyr))
suppressMessages(library(ggplot2))

setwd('/data1/liliuyang/MASH-OCEAN/20230721/test/user/M1S4_output/KO_Sample_by_pathway')
##########################      Sample-KO depth input      ##########################
sub_Sample_KO = read.csv('MASH.20230813.test.KO_Sample.wide.Photosynthesis.tsv', sep='\t',header = TRUE, row.names = 1)

## 热图
sub_Sample_KO %>%
  #{log10(.+1)} %>%
  #{log2(.+1)} %>%
  {pheatmap(.,
            method = 'average',
            na_col='grey',
            cluster_rows = F,
            cluster_cols = T,###是否对行列聚类
            show_rownames = T,show_colnames = T,###是否显示行名列名
            cellwidth = 7,cellheight = 6,###固定每个cell的宽和高
            display_numbers = F,####是否显示每个单元格的值
            fontsize_row = 6,
            fontsize_col	= 5,
            angle_col = 45
  )} %>%
{ggsave(paste0('MASH.20230813.test.KO_Sample.wide.Photosynthesis.pdf'),
        ., width = 14, height = 20)
}
