#!/bin/bash
:<<!EOF!
 * @Date: 2023-06-14 16:58:11
 * @LastEditors: Liuyang<PERSON>i
 * @LastEditTime: 2023-09-06 21:09:19
 * @FilePath: /liliuyang/R/Rscript.MASH/202308-v5/M1S1.MetaG-contigs.Fun.eggnog.sh
 * @Description:
!EOF!

help_message () {
	echo ""
	echo "Usage: M1S1.MetaG-contigs.Fun.eggnog.sh [options] -a emapper.txt -c bbmap.txt -s script_dir -p SRR0001 -o output_dir"
	echo "Note1: Make sure to provide directory and file prefix."
	echo ""
	echo "Options:"
	echo ""
	echo "	-a STR          file for prefix_contig1000.emapper.annotations"
	echo "	-c STR          file for prefix_depth_bbmap.txt"
	echo "	-s STR          directory for script"
	echo "	-p STR          prefix for each sample"
	echo "	-o STR          directory for output and error files (if exist). e.g., prefix.orf_KO_depth.tsv, prefix.error_KO_depth.tsv"
	echo "";}

########################################################################################################
########################               LOADING IN THE PARAMETERS                ########################
########################################################################################################
#
## load in params
OPTS=`getopt -o ha:d:s:p:o: --long annotations:,depth_contig:,script_DIR:,prefix:,output_DIR:,help -- "$@"`
if [ $? -ne 0 ]; then help_message; exit 1; fi
# loop through input params
while true; do
		case "$1" in
				-a) annotations=$2; shift 2;;
				-d) depth_contig=$2; shift 2;;
				-s) script_DIR=$2; shift 2;;
				-p) prefix=$2; shift 2;;
				-o) output_DIR=$2; shift 2;;
				-h | --help) help_message; exit 1; shift 1;;
				--) help_message; exit 1; shift; break;;
				*) break;;
		esac
done

########################################################################################################
########################           MAKING SURE EVERYTHING IS SET UP             ########################
########################################################################################################

sample=${prefix}
input_annotations=${annotations} #${sample}.contig1000.emapper.annotations
input_contig_depth=${depth_contig} #${sample}_depth_bbmap.txt

output_orf_KO_depth=${output_DIR}/AAA_orf_KO_depth/${sample}.orf_KO_depth.tsv
output_KO_depth=${output_DIR}/AAA_KO_depth/${sample}.KO_depth.tsv

output_tmp_annotations=${output_DIR}/tmp_contigs_emapper/${sample}.contig1000.emapper.annotations.scale
output_tmp_orf_KO=${output_DIR}/tmp_contigs_emapper/${sample}.orf_KO.tsv
output_error_mapping=${output_DIR}/tmp_contigs_emapper/${sample}.error_mapping.tsv

#check if the prefix exists
if [ -z $prefix ]; then
	echo "Please input the prefix. Exiting..."
	help_message; exit 1
fi


#check if the emapper annotation file exists
if [ -z ${input_annotations} ]; then
	echo "Please input the emapper annotation file."
	help_message; exit 1
else
	if [ ! -s $input_annotations ]; then
		echo "$input_annotations does not exist. Exiting..."
		help_message; exit 1
	fi
fi

#check if the contig_depth file exists
if [ -z ${input_contig_depth} ]; then
	echo "Please input the contig_depth file."
	help_message; exit 1
else
	if [ ! -s $input_contig_depth ]; then
		echo "$input_contig_depth does not exist. Exiting..."
		help_message; exit 1
	fi
fi

# Checks for scripts folder
if [ -z ${script_DIR} ]; then
	echo "Please input the scripts directory."
	help_message; exit 1
else
	if [ ! -s ${script_DIR}/M1S1.ko.depth.cmd.R ]; then
		echo "The file ${script_DIR}/M1S1.ko.depth.cmd.R does not exist."
		help_message; exit 1
	fi
fi

#check if the output_DIR dir exists
if [ -z ${output_DIR} ]; then
	echo "Please input the output_DIR."
	help_message; exit 1
fi
########################################################################################################
########################                    BEGIN PIPELINE!                     ########################
########################################################################################################

eval "$(conda shell.bash hook)"
conda activate R4.2


if [ ! -d ${output_DIR} ]; then mkdir -p ${output_DIR};
else
	echo "Warning: ${output_DIR} already exists."
fi

if [ ! -d ${output_DIR}/AAA_KO_depth/ ]; then mkdir -p ${output_DIR}/AAA_KO_depth/;
else
	echo "Warning: ${output_DIR}/AAA_KO_depth/ already exists."
	if [ -f ${output_KO_depth} ]; then rm -r ${output_KO_depth}; fi
fi

if [ ! -d ${output_DIR}/AAA_orf_KO_depth/ ]; then mkdir -p ${output_DIR}/AAA_orf_KO_depth/;
else
	echo "Warning: ${output_DIR}/AAA_orf_KO_depth/ already exists."
	if [ -f ${output_orf_KO_depth} ]; then rm -r ${output_orf_KO_depth}; fi
fi

if [ ! -d ${output_DIR}/tmp_contigs_emapper/ ]; then mkdir -p ${output_DIR}/tmp_contigs_emapper/;
else
	echo "Warning: ${output_DIR}/tmp_contigs_emapper/ already exists."
	if [ -f ${output_tmp_annotations} ]; then rm -r ${output_tmp_annotations}; fi
	if [ -f ${output_tmp_orf_KO} ]; then rm -r ${output_tmp_orf_KO}; fi
	if [ -f ${output_error_mapping} ]; then rm -r ${output_error_mapping}; fi
fi


awk -F '\t' 'BEGIN {OFS="\t"} {print $1,$12}' ${input_annotations} > ${output_tmp_annotations}

num=3 # 要删除的行数
max=`sed -n '$=' ${output_tmp_annotations}` # 文件总行数
let sLine=max-num+1   # 删除的起始行
sed $sLine',$d' ${output_tmp_annotations} | sed '1,5d' > ${output_tmp_orf_KO} #从起始行删除到最后行,再删除前5行

Rscript ${script_DIR}/M1S1.ko.depth.cmd.R \
    --in_orf_KO ${output_tmp_orf_KO} \
    --in_contig_depth ${input_contig_depth} \
    --out_orf_KO_depth ${output_orf_KO_depth} \
    --out_KO_depth ${output_KO_depth} \
		--out_error_mapping ${output_error_mapping}


if [ -f ${output_orf_KO_depth} ];
then
	# R代码输出的文件至少包含一行，不需要其他判断
	sed -i '1d' ${output_orf_KO_depth}
fi

if [ -f ${output_KO_depth} ];
then
	sed -i '1d' ${output_KO_depth}
fi
########################################################################################################
########################      SCALE KO AND ABUNDANCE PIPELINE SUCCESSFULLY FINISHED!!!         ########################
########################################################################################################
echo "SCALE KO AND ABUNDANCE PIPELINE SUCCESSFULLY FINISHED!!!"
