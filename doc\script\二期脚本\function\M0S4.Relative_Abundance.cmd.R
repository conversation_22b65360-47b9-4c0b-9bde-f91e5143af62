###
#' @Date: 2023-08-18 20:24:02
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-03-18 21:45:17
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v7/M0S4.Relative_Abundance.cmd.R
#' @Description:
###

suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "user_Sample_in_file=s", "user_Sample file",
  "out_DIR=s", "output directory",
  "prefix=s", "prefix",
  "verbose!","print messages"
)

# 'MASH/M1S2_output/MASH.20230818.test.KO_Sample.wide.tsv'
#result <- data.table(read.delim(MASH_Sample_in_file,quote='',sep = '\t',header = T, check.names = FALSE))

if(! dir.exists(out_DIR)){
  dir.create(out_DIR, recursive = T)
}

Sample_table = read.csv(user_Sample_in_file, quote='', sep='\t',header = TRUE, check.names = FALSE, row.names = 1)

# 保存全0列和全0行的名称
zero_cols <- colnames(Sample_table)[colSums(Sample_table) == 0]
zero_rows <- rownames(Sample_table)[rowSums(Sample_table) == 0]

# 删除全0行和全0列，并进行运算
Sample_table_scale = Sample_table %>%
  {.[rowSums(.)>0,, drop = F]} %>%
  {.[,colSums(.)>0, drop = F]} %>%
  {as.data.frame(t(.))} %>%
  {./rowSums(.)} %>%
  {as.data.frame(t(.))} %>%
  {((.)*1000000)}

# 将全0列和全0行补回来
for (col in zero_cols) {
  Sample_table_scale[, col] <- 0
}
for (row in zero_rows) {
  Sample_table_scale[row, ] <- 0
}

Sample_table_scale %>%
  {mutate(., Orthology_Entry = rownames(.))} %>%
  {.[,c('Orthology_Entry', colnames(.)[(colnames(.) != 'Orthology_Entry')]), drop = F]} %>%
  {write.table(., paste0(out_DIR, '/', prefix, '.KO_Sample.wide.RA.tsv'), row.names = FALSE, col.names = T,
            sep = '\t', quote = FALSE, fileEncoding="UTF-8")}

print("M0S4.Relative_Abundance.cmd.R finished!")
