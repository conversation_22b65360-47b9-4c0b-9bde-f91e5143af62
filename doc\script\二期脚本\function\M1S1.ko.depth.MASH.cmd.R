###
#' @Date: 2022-10-24 13:57:01
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-30 11:13:14
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v4/M1S1.ko.depth.cmd.R
#' @Description:
###
suppressMessages(library(GetoptLong))
#setwd('/data1/liliuyang/photohydro/20230309/data/AAA_Rplot')
rm(list = ls(all.names = TRUE))
packages=c("data.table","reshape2","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_orf_KO=s", "eggnog ko and contig-cds name",
  "in_contig_depth=s", "input abundance",
  "out_orf_KO_depth=s", "output",
  "out_KO_depth=s", "output",
  "out_error_mapping=s", "output",
  "verbose!","print messages"
)


multi_depth_KO_to_one = function(data = cekd.orfX){
  KO_str = data %>%
    {.[, 'KO']} %>%
    {unlist(strsplit(., ","))}
  mean_depth = (data$Avg_fold)/length(KO_str)
  result = cbind(data$orf, KO_str, mean_depth)
  #colnames(result) = c('orf','KO','Avg_fold')
  return(result)
}

seperate_emapper_ko_depth = function(data = contig1000_emapper_KO_depth){
  cekd.m = data %>% {.[c(grep(pattern=",", .$KO)),]}
  cekd.s = data %>% {.[!(.$orf %in% cekd.m$orf),]}

  if (length(cekd.m$KO)>0){
    cekd.trans = data.frame()
    for (orf in cekd.m$orf) {
      cekd.orfX = cekd.m %>%
        {.[(.$orf %in% orf),,drop=F]} %>%
        {multi_depth_KO_to_one(.)}
      cekd.trans = rbind(cekd.trans, cekd.orfX)
    }

    colnames(cekd.trans) = c('orf','KO','Avg_fold')

    if (length(cekd.s$KO)>0){
      result = rbind(cekd.trans, cekd.s)
    } else {
      result = cekd.trans
    }

  } else {
    result = cekd.s
  }
  return(result)
}


#导入深度，进行合并out_orf_KO_depth
# '/data1/liliuyang/MASH-OCEAN/20230613/data/AAA_bbmap_depth/SRR5229878_depth_bbmap.txt'
contig1000_depth =  in_contig_depth %>%
  read.csv(., sep = '\t', quote='', header = T, col.names = c('Contig','Avg_fold'))

# 1 input '/data1/liliuyang/MASH-OCEAN/20230613/data/tmp_orf_KO/SRR5229878.orf_KO.tsv'
contig1000_emapper_KO_depth = in_orf_KO %>%
  read.csv(., sep = '\t', quote='', header = F, col.names = c('orf','KO')) %>%
  {.[!(.$KO %in% '-'),]}

if (length(contig1000_emapper_KO_depth$orf)>0){
  length_insect_orf = contig1000_emapper_KO_depth$orf %>%
    {length(.[(.) %in% contig1000_depth$Contig])}
  if (length(contig1000_emapper_KO_depth$orf) == length_insect_orf){
    print("The Gene Abundance file is orf abundance!")
    colnames(contig1000_depth) = c('orf','Avg_fold')
    contig1000_emapper_KO_depth = contig1000_emapper_KO_depth %>%
      {data.table(.)} %>%
      merge.data.table(., contig1000_depth, all.x = T, by = 'orf') %>%
      select(., c('orf','KO','Avg_fold')) %>%
      {data.frame(.)}
  } else {
    contig1000_emapper_KO_depth = contig1000_emapper_KO_depth %>%
      {mutate(., Contig = sapply(as.character(.$orf),
                                function(x) unlist(strsplit(x, "_\\d*$"))[1]))}
    length_insect_contig = contig1000_emapper_KO_depth$Contig %>%
      {length(.[(.) %in% contig1000_depth$Contig])}
    if (length(contig1000_emapper_KO_depth$Contig) == length_insect_contig){
      print("The Gene Abundance file is contig mean abundance!")
      contig1000_emapper_KO_depth = contig1000_emapper_KO_depth %>%
        {data.table(.)} %>%
        merge.data.table(., contig1000_depth, all.x = T, by = 'Contig') %>%
        select(., c('orf','KO','Avg_fold')) %>%
        {data.frame(.)}
    } else {
      file.create(out_error_mapping)
      stop("Please check the mapping relationship between the Gene ID file and Gene Abundance file!")
    }
  }

  orf_KO_depth = contig1000_emapper_KO_depth %>%
    {seperate_emapper_ko_depth(.)} %>%
    mutate(.,
           KO = gsub('ko:','', .$KO),
           Avg_fold = round(as.numeric(.$Avg_fold), 4)
    )
  orf_KO_depth %>%
    {write.table(., out_orf_KO_depth, row.names = FALSE, col.names = T,
                 sep = '\t', quote = FALSE, fileEncoding="UTF-8")}


  KO_depth = orf_KO_depth %>%
    {aggregate(
      x=list(Avg_fold = (.[,"Avg_fold"])),
      by=list(KO = (.[,"KO"])
      ),
      FUN=sum
    )}
  KO_depth %>%
    {write.table(., out_KO_depth, row.names = FALSE, col.names = T,
                 sep = '\t', quote = FALSE, fileEncoding="UTF-8")}

} else {

  orf_KO_depth = as.data.frame(t(c('orf','KO','Avg_fold')))
  orf_KO_depth %>%
    {write.table(., out_orf_KO_depth, row.names = FALSE, col.names = FALSE,
                 sep = '\t', quote = FALSE, fileEncoding="UTF-8")}

  KO_depth = as.data.frame(t(c('KO','Avg_fold')))
  KO_depth %>%
    {write.table(., out_KO_depth, row.names = FALSE, col.names = FALSE,
                 sep = '\t', quote = FALSE, fileEncoding="UTF-8")}

}



print("M1S1.ko.depth.cmd.R finished!")
