server:
  port: 8080
  tomcat:
    uri-encoding: UTF-8

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    name: mash-web
  profiles:
    active: @spring.profiles.active@
  # 邮件
  mail:
    # 默认编码
    default-encoding: UTF-8
    host: mail.cstnet.cn
    # username: <EMAIL>
    # password: 4aa5q@qDeK$Z~tZH
    username: <EMAIL>
    password: nXAaY*7H&^Cgu3%q
    port: 994
    properties.mail.smtp:
      ssl:
        enable: true
      socketFactory:
        class: javax.net.ssl.SSLSocketFactory

app:
  # Scholarly Archive页面中显示的最近文章（Most recent）
  most-recent:
    - pmid: 38365240
      img: mostRecent1.png
    - pmid: 38233386
      img: mostRecent2.png
    - pmid: 39569004
      img: mostRecent3.png
    - pmid: 38862603
      img: mostRecent4.png
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # 验证码开关
  captchaEnabled: true