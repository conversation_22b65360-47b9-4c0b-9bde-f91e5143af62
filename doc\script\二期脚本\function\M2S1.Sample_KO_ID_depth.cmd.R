###
#' @Date: 2022-10-24 13:57:01
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-16 20:49:33
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M2S1.Sample_KO_ID_depth.cmd.R
#' @Description:
###
suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("reshape2","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_user_KO=s", "eggnog ko and contig-cds name",
  "in_MASH_Sample_KO_depth=s", "eggnog ko, contig-cds name, depth column",
  "out_DIR_MASH_Sample_per_KO=s", "output directory",
  "out_DIR_MASH_error=s", "output error directory",
  "prefix=s", "prefix",
  "verbose!","print messages"
)

# 1 user KO_ID has been verified the presence in KO_list, 1 KO per Batch.
# '/data1/liliuyang/MASH-OCEAN/20230721/test/user_gene/AAA_KO_ID/AAAtest1.gene.KO'
user_KO_ID = in_user_KO %>%
  read.csv(., sep = '\t', quote='', header = F) %>%
  {.[, 1]}

# 2 MASH
MASH_contig1000_emapper_KO_depth = in_MASH_Sample_KO_depth %>%
  read.csv(., sep = '\t', quote='', header = F) %>%
  {.[, 1:2]}
colnames(MASH_contig1000_emapper_KO_depth) = c('Sample_KO', 'Avg_fold')



if (length(MASH_contig1000_emapper_KO_depth$Sample_KO)>0){

  MASH_Sample_KO = MASH_contig1000_emapper_KO_depth %>%
    mutate(.,
          Sample = sapply(as.character(.$Sample_KO),
                          function(x) unlist(strsplit(x, "---"))[1]),
          KO = sapply(as.character(.$Sample_KO),
                          function(x) unlist(strsplit(x, "---"))[2])
    ) %>%
    {select(., c('Sample', 'KO', 'Avg_fold'))} %>%
    {unique(.)} %>%
    {.[(.$KO %in% unique(user_KO_ID)),,drop=F]}

  for (KO in unique(MASH_Sample_KO$KO)) {
    MASH_Sample_per_KO = MASH_Sample_KO %>%
      {.[(.$KO %in% KO),c('Sample','Avg_fold'),drop=F]} %>%
      {unique(.)}
    MASH_Sample_per_KO %>%
      {write.table(., paste0(out_DIR_MASH_Sample_per_KO, '/', prefix, '.MASH_Sample.', KO, '.tsv'), row.names = FALSE, col.names = FALSE,
                  sep = '\t', quote = FALSE, fileEncoding="UTF-8")}
  }

} else {

  file.create(paste0(out_DIR_MASH_error, '/', prefix, '.error.tsv'))

}

print("M2S1.Sample_KO_ID.cmd.R finished!")
