###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-04-08 12:07:42
#' @FilePath: /liliuyang/R/Rscript.MASH/20240407/kraken.Barplot_lefse.cmd.R
#' @Description:

suppressMessages(library(GetoptLong))
suppressMessages(library(ggplot2))
suppressMessages(library(tidyverse))
suppressMessages(library(plotly))
suppressMessages(library(reshape2))
# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Group_color=s", "group table, e.g., group_color.txt with two coloumn: Group and Color",
  "Input=s", "input Dir for PCoA Table, e.g., PCoA_Table",
  "Output=s", "output Dir for PCoA figure, e.g., PCoA_Figure",
  "verbose!","print messages"
)


plot_graph <- function(file_LDA, levels_group, Group_color_each_map, levels_OTU) {

  if (length(levels_group) == 2) {
    ############################## 两组的情况 ##############################
    file_LDA <- file_LDA %>%
      mutate(LDA_score = ifelse(group %in% levels_group[2], -1 * LDA_score, LDA_score))

    plot_LDA = file_LDA %>%
      {mutate(.,
              Group = factor(.$group, levels = levels_group)
      )} %>%
      arrange(Group, desc(LDA_score)) %>%
      {mutate(.,
              OTU = factor(.$OTU, levels = rev(unique(.$OTU)))
      )}

    p1 = ggplot(plot_LDA, aes(x = OTU, y = LDA_score, fill = Group)) +
      geom_bar(stat = 'identity', width = 0.6) +
      coord_flip() +
      geom_text(data = subset(plot_LDA, LDA_score < 0),
                aes(x=OTU, y= 0, label = paste0(" ", OTU)),#bar跟坐标轴间留出间隙
                size = 5, color = 'black', #字的大小
                hjust = "bottom" ) +  #字的对齐方式
      geom_text(data = subset(plot_LDA, LDA_score > 0),
                aes(x=OTU, y= 0, label = paste0(OTU," ")),
                size = 5, color = 'black', hjust = "inward" ,angle=360) +
      scale_fill_manual(values = Group_color_each_map$Color) +
      scale_colour_manual(values = Group_color_each_map$Color) +
      theme(
        plot.title = element_text(hjust = 0.5),
        title = element_text(family = 'serif', size = 20),
        axis.text.x = element_text(size = (15 - length(levels_OTU) / 20)),
        axis.text.y = element_blank(),
        axis.ticks = element_blank(),
        legend.title = element_blank(),
        legend.text = element_text(family = 'serif', size = 15),
        panel.background = element_rect(fill = NA),
        panel.grid.major.x = element_line(colour = "grey", linetype = "dashed"),
        axis.title.y = element_text(margin = margin(t = 0, r = 0, b = 0, l = 0))
      ) +
      labs(x = NULL, y = 'LDA Score (log 10)')

  } else if (length(levels_group) > 2 | length(levels_group) == 1) {

    plot_LDA = file_LDA %>%
      {mutate(.,
              Group = factor(.$group, levels = levels_group)
      )} %>%
      arrange(Group, desc(LDA_score)) %>%
      {mutate(.,
              OTU = factor(.$OTU, levels = rev(unique(.$OTU)))
      )}

    ############################## 1345组的情况 ##############################

    p1 = ggplot(plot_LDA, aes(x = OTU, y = LDA_score, fill = Group)) +
      geom_bar(stat = 'identity', width = 0.6) +
      coord_flip() +
      scale_fill_manual(values = Group_color_each_map$Color) +
      scale_colour_manual(values = Group_color_each_map$Color) +
      theme(
        plot.title = element_text(hjust = 0),
        title = element_text(family = 'serif', size = 20),
        axis.text.x = element_text(size = (15 - length(levels_OTU) / 20)),
        axis.text.y = element_text(hjust = 0, size = (15 - length(levels_OTU) / 20)),
        axis.ticks = element_blank(),
        legend.title = element_blank(),
        legend.text = element_text(family = 'serif', size = 15),
        panel.background = element_rect(fill = NA),
        panel.grid.major.x = element_line(colour = "grey", linetype = "dashed"),
        axis.title.y = element_text(margin = margin(t = 0, r = 0, b = 0, l = 0))  # 修改这里
      ) +
      labs(x = NULL, y = 'LDA Score (log 10)')

  }
  return(p1)
}

##########################      Data input      ##########################
# Group = "group.tsv"
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1,check.names = F)
levels_group = sort(unique(group$group))
group$group=factor(group$group,levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列

#Group_color = 'group_color.tsv'
Group_color_data = read.csv(Group_color, sep='\t',header = TRUE, col.names = c('Group', 'Color'),
                            comment.char="", stringsAsFactors = FALSE,check.names = F) %>%
  {.[.$Group %in% unique(group$group), ]}
rownames(Group_color_data) = Group_color_data$Group


if(! dir.exists(Output)){
  dir.create(Output)
}
################################## for循环跑每个类别 ##################################
for (domain in c('A', 'B', 'E', 'V')) {
  print(paste0('Domain: ',domain))
  for (Beta_level in c('phylum', 'class', 'order', 'family', 'genus', 'species')) {
    print(paste0('Beta_level: ',Beta_level))

    LDA_file_name = paste0(Input,"/",domain,"/",Beta_level,".res")
    #LDA_file_name = 'species.res'
    if(file.exists(LDA_file_name)){

      file_LDA = read.csv(LDA_file_name ,sep='\t', header = F, stringsAsFactors = FALSE) %>%
        {.[c(1,3,4)]}
      colnames(file_LDA) = c('OTU','group','LDA_score')
      file_LDA$LDA_score = as.numeric(file_LDA$LDA_score)
      file_LDA = file_LDA %>%
        {.[!((.$LDA_score %in% 'NA')|is.na(.$LDA_score)) ,]} %>%
        {.[!(is.na(.$group)) ,]} %>%
      group_by(group) %>%
      top_n(10, LDA_score) %>%
      ungroup()

      levels_OTU = length(file_LDA$OTU) # 1~50

      plot_height = 2 + 0.12*levels_OTU

      levels_group = group %>%
        {.[.$group %in% unique(file_LDA$group), ]} %>%
        {sort(unique(.$group))}

      Group_color_each_map =  Group_color_data %>%
        {.[.$Group %in% levels_group, ]} %>%
        {.[match(levels_group,rownames(.)),]} %>%
        {mutate(., group = (.$Group))}


      if (length(levels_group) > 0){
        p3 = plot_graph(file_LDA, levels_group, Group_color_each_map, levels_OTU)
        p3 %>%
          ggsave(paste0("Lefse.", domain, ".", toupper(substr(Beta_level, 1, 1)), ".pdf"),
                path = Output, .,width = 10, height = plot_height, limitsize = F)
      }

    } else {
    print("文件不存在")
    }
  }
}
