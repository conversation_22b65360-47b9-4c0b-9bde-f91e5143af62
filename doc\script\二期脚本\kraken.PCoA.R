###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangL<PERSON>
#' @LastEditTime: 2022-11-11 23:17:43
#' @FilePath: /R/Rscript.kraken/kraken.PCoA.R
#' @Description:

suppressMessages(library(GetoptLong))
suppressMessages(library(ggplot2))
suppressMessages(library(tidyverse))
library(htmlwidgets)
library(plotly)

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Input=s", "input Dir for PCoA Table, e.g., PCoA_Table",
  "Output=s", "output Dir for PCoA figure, e.g., PCoA_Figure",
  "verbose!","print messages"
)
##########################      Data input      ##########################
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1)
levels_group = sort(unique(group$group))
group$group=factor(group$group,
                            levels = levels_group)
group$sample_name <- rownames(group) #添加sample_name列

# if specify the group name as c("GroupA","GroupB","GroupC","GroupD","GroupE")
color_theme_group = data.frame(
  Group = c("GroupA","GroupB","GroupC","GroupD","GroupE"),
  Color = c("#377EB8","#FF7F00","#E41A1C","#4DAF4A","#984EA3")
) %>%
  {.[.$Group %in% unique(group$group), 'Color']}

if(! dir.exists(Output)){
  dir.create(Output)
}
################################## for循环跑每个类别 ##################################

for (domain in c('A', 'B', 'E', 'V')) {
  for (beta in c('bray', 'jaccard')) {
    print(paste0('Method: ',beta))
    ## PCoA
    label = read.csv(paste0(Input, "/", "PCoA.label.", domain, ".", beta, ".csv"), sep=',',header = TRUE)
    plot.pcoa = read.csv(paste0(paste0(Input, "/",  "PCoA.", domain, ".", beta, ".csv")), sep=',',header = TRUE)

    # p = read.csv(paste0(paste0(Input, "/",  "PCoA.", domain, ".", beta, ".csv")), sep=',',header = TRUE) %>%
    #   {ggplot(data=.,aes(x=Axis.1,y=Axis.2,
    #                                 color=group,shape=group))+ # 导入4列数据sample_name,Axis.1(横轴),Axis.2(纵轴),group
    #       stat_ellipse(geom = "polygon",
    #                    aes(fill=group),
    #                    alpha=0.25)+ # 置信椭圆 颜色按照分组，透明度0.25
    #       geom_point(size=5)+
    #       #scale_fill_manual(values = c("#A3A500", "#00BF7D", "#00B0F6", "#E76BF3","#F8766D"))+
    #       geom_vline(xintercept = 0,lty="dashed")+ # 在0坐标增加一条垂直虚线
    #       geom_hline(yintercept = 0,lty="dashed")+ # 在0坐标增加一条水平虚线
    #       labs(x=paste0("PCoA1 (",label$x_label,"%)"),
    #            y=paste0("PCoA2 (",label$y_label,"%)"))+ # 坐标标签设置
    #       theme_bw()+
    #       theme(panel.grid = element_blank()) # 删除网格线
    #     }
    #   # {ggsave(paste0("PCoA.", domain, ".", beta, ".png"),
    #   #        path = Output, .,
    #   #        width = 10, height = 8)
    #   # }
    #   saveWidget(ggplotly(p), file = paste0(Output,"/PCoA.", domain, ".", beta, ".html"))
    #   ggsave(paste0("PCoA.", domain, ".", beta, ".png"),path = Output, p,width = 10, height = 8)
    p3 = plot.pcoa %>%
      {ggplot(data=.,aes(x=Axis.1,y=Axis.2,
                         color=group,shape=group,fill = group))+ # 导入4列数据sample_name,Axis.1(横轴),Axis.2(纵轴),group
          stat_ellipse(geom = "polygon",

                       alpha = 0.15)+ # 置信椭圆 颜色按照分组，透明度0.25
          geom_point(size=5)+
          scale_fill_manual(values = color_theme_group[1:length(unique(group$group))])+
          scale_color_manual(values = color_theme_group[1:length(unique(group$group))])+
          #geom_vline(xintercept = 0,lty="dashed")+ # 在0坐标增加一条垂直虚线
          #geom_hline(yintercept = 0,lty="dashed")+ # 在0坐标增加一条水平虚线
          labs(x=paste0("PCo1 (",label$x_label,"%)"),
               y=paste0("PCo2 (",label$y_label,"%)"))+ # 坐标标签设置
          theme_bw()+
          theme(panel.grid = element_blank()) # 删除网格线
        }
    p3 %>%
      ggsave(paste0("PCoA.", domain, ".", beta, ".png"),path = Output, .,width = 10, height = 8)
    mytext <- paste("PCo1: ", sprintf("%0.4f",plot.pcoa$Axis.1), "\n" ,
                    "PCo2: ", sprintf("%0.4f",plot.pcoa$Axis.2), "\n",
                    "Run ID: ", (plot.pcoa$sample_name), "\n",  sep="")
    pp <- p3 %>%
      {plotly_build(.)} %>%
      {style(., text=mytext, hoverinfo = "text")} %>%
      # hide_legend(.) %>%
      {saveWidget(., file = paste0(Output,"/PCoA.", domain, ".", beta, ".html"),
                                             libdir = "./HTMLdependencies")}
  }
}
