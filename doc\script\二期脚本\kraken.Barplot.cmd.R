###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-04-07 17:26:15
#' @FilePath: /liliuyang/R/Rscript.MASH/20240407/kraken.Barplot.cmd.R
#' @Description:
###

suppressMessages(library(GetoptLong))
suppressMessages(library(ggplot2))
suppressMessages(library(tidyverse))
suppressMessages(library(plotly))
suppressMessages(library(htmlwidgets))
suppressMessages(library(reshape2))
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Input=s", "input Dir for Barplot Table, e.g., Barplot_Table",
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Output=s", "output Dir for Barplot figure, e.g., Barplot_Figure",
  "verbose!","print messages"
)

##########################      Data input      ##########################

#Group = 'Input_Table/group.tsv'
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1,check.names = F)
group$sample_name <- rownames(group) #添加sample_name列
names(group)[1] = 'Station'
################################## for循环跑每个类别 ##################################
color_theme = c(
  "#0288D1","#FF9800","#F44336","#26A69A","#FFCA28","#26C6DA",
  "#FFEE58","#009688","#8BC34A","#AB47BC","#CDDC39","#FFC107",
  "#E91E63","#9CCC65","#795548","#9C27B0","#3F51B5","#42A5F5",
  "#EF5350","#00BCD4","#66BB6A","#FF5722","#E6EE9C","#3F51B5",
  "#FFEB3B","#D4E157","#673AB7","#4CAF50","#EC407A","#9E9E9E"
)
if(! dir.exists(Output)){
  dir.create(Output)
}

for (domain in c('A', 'B', 'E', 'V')) {
  print(paste0('Domain: ',domain))
  for (taxonomy in c('P', 'C', 'O', 'F', 'G', 'S')) {
    print(paste0('Taxonomy: ',taxonomy))
    file_name = paste0(Input, "/", "Barplot.", domain, ".", taxonomy, ".csv")
    if(file.exists(file_name)){
      Barlegend_file_name = paste0(Input, "/", "Barlegend.", domain, ".", taxonomy, ".csv")
      if(file.exists(Barlegend_file_name)){

        levels_taxonomy <-read.table(Barlegend_file_name, header=T,sep=",", quote = "",
                                comment.char="",stringsAsFactors = FALSE) %>%
          {.[,'Tax']}

        levels_sample_names = read.csv(file_name ,sep=',', header = TRUE, stringsAsFactors = FALSE,check.names = F) %>%
          {.[,'sample_name']} %>%
          {unique(.)}

        data = read.csv(file_name ,sep=',', header = TRUE, stringsAsFactors = FALSE,check.names = F) %>%
          {mutate(., sample_name = factor((.$sample_name), levels = (levels_sample_names)))} %>%
          {mutate(., Taxonomy = factor(.$Taxonomy, levels = (levels_taxonomy)))} %>%
          {merge(., group, by = 'sample_name', all.x = T)}
        #print(data)
        #print(group)

        num_sample = length(unique(levels_sample_names))
        print(paste0("num of sample: ", num_sample))
        # 按照线性关系width=3+0.5*num_group
        figure_width = 24 + 0.1*num_sample
        if (figure_width > 49){
          figure_width = 49
        }

        figure_vjust = 1.5 + 0.005*num_sample
        if (figure_vjust > 2){
          figure_vjust = 2
        }
        figure_text_size = (15 - length(levels_sample_names)/20)
        if (figure_text_size < 5){
          figure_text_size = 5
        }

        p = data %>%
          {ggplot(.,aes(x=sample_name,y=Abudance,fill=Taxonomy))+
              geom_bar(stat = "identity", width = 0.7, position = position_stack(reverse = TRUE)) + #柱状图每根柱子的宽度,方向为倒向
              scale_fill_manual(values = color_theme)+ # 按照物种分类学填充颜色,颜色主题是color_theme
              facet_grid(.~Station,scales ='free_x')+
              theme(plot.title = element_text(hjust=0.5), # title 水平调整0.5
                    title=element_text(family = 'serif',size=20), # title字体为serif，字体大小20
                    #axis.text.x = element_blank(),
                    axis.text.x = element_text(angle = 45, hjust = 1, vjust = figure_vjust, size= figure_text_size), # 横轴标签左倾斜45°,为了紧贴柱子,标签水平调整1,垂直调整1.7单位
                    axis.text.y = element_text(hjust = 1, size= figure_text_size),
                    axis.ticks.x = element_blank(), # 删去刻度线
                    legend.title=element_blank(), # legend 的title为空
                    legend.position = 'bottom', # 图例展示放于图片的下方
                    legend.text = element_text(family='serif',size = 15), # legend字体为serif，字体大小15
                    strip.text = element_text(size = 24),
                    panel.background = element_rect(fill=NA), # 背景为空
                    panel.grid = element_blank()) + # 网格线为空
              labs(x=NULL,y='Relative Abundance (%)') +
              guides(fill=guide_legend(ncol = 6, byrow = T, reverse = F, label.position = 'right'))
          }
          #saveWidget(ggplotly(p), file = paste0(Output,"/Barplot.", domain, ".", taxonomy, ".html"))
          ggsave(paste0("Barplot.", domain, ".", taxonomy, ".pdf"), p,path = Output,width = figure_width, height = 12)
      }
    }
  }
}
