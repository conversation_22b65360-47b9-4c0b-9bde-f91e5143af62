#! /bin/bash
path="/data/mash"
group_column="group"
scripts_path="/data/mash/script/lefse/scripts"
images_path="/data/mash/script/lefse/images"

input_file=$1 
output_file=$2
metadata=$3

set -e

# # 可能需要修改调用脚本lefse_format.py的位置及挂载目录 /bdp-picb/hpcimage/16S_scripts/lefse_format.py
# # singularity run -B ${path}:${path} -B ${scripts_path}:/scripts ${images_path}/conda-scipy.sif \
# python ${scripts_path}/diff_scripts/lefse_format.py \
# -i ${input_file} \
# -m ${metadata} \
# -c ${group_column} \
# -o ${output_file}.txt

# python ${scripts_path}/diff_scripts/lefse_format.py \
# -i ${input_file} \
# -m ${metadata} \
# -c ${group_column} \
# -o ${output_file}.txt
# # singularity run -B ${path}:${path} -B ${scripts_path}:/scripts ${images_path}/lefse-1.0.0.sif \
/opt/lefse-master/format_input.py ${output_file}.txt ${output_file}.in -c 1 -o 1000

# # singularity run -B ${path}:${path}  -B ${scripts_path}:/scripts ${images_path}/lefse-1.0.0.sif \
# # python  -W ignore::RuntimeWarning /opt/conda/bin/
python  -W ignore::RuntimeWarning /opt/lefse-master/run_lefse.py ${output_file}.in ${output_file}.res

# #修改plot_res.py的位置，原位置为${images_path}/16S_scripts/lefse-1.0.0_dev_9adc3a62460e-py27r35_0/bin
# # singularity run -B ${path}:${path} -B ${scripts_path}:/scripts ${images_path}/lefse-1.0.0.sif \
python ${scripts_path}/diff_scripts/plot_res.py --format pdf ${output_file}.res ${output_file}_res_plot.pdf

# # singularity run -B ${path}:${path} -B ${scripts_path}:/scripts ${images_path}/lefse-1.0.0.sif \
python ${scripts_path}/diff_scripts/plot_cladogram.py --format pdf ${output_file}.res ${output_file}_cladogram_plot.pdf

