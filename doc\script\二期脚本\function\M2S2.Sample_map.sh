#!/bin/bash
:<<!EOF!
 * @Date: 2023-06-14 16:58:11
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-08-17 21:17:49
 * @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M2S2.Sample_map.sh
 * @Description: 每个批次的prefix要唯一
!EOF!

help_message () {
	echo ""
	echo "Usage: M2S2.Sample_map.sh [options] -u MASH_Sample_per_KO -m Metadata.trans_filt_MASH_BDC_2022_11_21.tsv -p 20230812 -o M2S2_output"
	echo "Note1: Make sure to provide directory and file prefix."
	echo ""
	echo "Options:"
	echo ""
	echo "	-u STR          directory for MASH_Sample per user KO"
	echo "	-m STR          file for the total MASH metadata file"
	echo "	-s STR          directory for script"
	echo "	-p STR          prefix for each sample list, should be unique for each batch"
	echo "	-o STR          directory for output files"
	echo "";}

########################################################################################################
########################               LOADING IN THE PARAMETERS                ########################
########################################################################################################
#
## load in params
OPTS=`getopt -o hu:m:s:p:o: --long user_input:,MASH_metadata:,script_DIR:,prefix:,output_DIR:,help -- "$@"`
if [ $? -ne 0 ]; then help_message; exit 1; fi
# loop through input params
while true; do
		case "$1" in
				-u) user_input=$2; shift 2;;
				-m) MASH_metadata=$2; shift 2;;
				-s) script_DIR=$2; shift 2;;
				-p) prefix=$2; shift 2;;
				-o) output_DIR=$2; shift 2;;
				-h | --help) help_message; exit 1; shift 1;;
				--) help_message; exit 1; shift; break;;
				*) break;;
		esac
done

########################################################################################################
########################           MAKING SURE EVERYTHING IS SET UP             ########################
########################################################################################################

#check if MASH_Sample per user KO file exists
if [ -z ${user_input} ]; then
	echo "Please input the directory of MASH_Sample per user KO."
	help_message; exit 1
else
	if [ ! -d $user_input ]; then
		echo "The directory $user_input does not exist. Exiting..."
		help_message; exit 1
	fi
fi

#check if the MASH metadata file exists
if [ -z ${MASH_metadata} ]; then
	echo "Please input MASH metadata file."
	help_message; exit 1
else
	if [ ! -s $MASH_metadata ]; then
		echo "The file $MASH_metadata does not exist. Exiting..."
		help_message; exit 1
	fi
fi

# Check for scripts folder
if [ -z ${script_DIR} ]; then
	echo "Please input the scripts directory."
	help_message; exit 1
else
	if [ ! -s ${script_DIR}/M2S2.Sample_map.cmd.R ]; then
		echo "The file ${script_DIR}/M2S2.Sample_map.cmd.R does not exist."
		help_message; exit 1
	fi
fi

#check if the prefix exists
if [ -z $prefix ]; then
	echo "Please input the prefix. Exiting..."
	help_message; exit 1
fi


#check if the output_DIR dir exists
if [ -z ${output_DIR} ]; then
	echo "Please input the output_DIR."
	help_message; exit 1
fi
########################################################################################################
########################                    BEGIN PIPELINE!                     ########################
########################################################################################################

output_DIR_AAA_gene_metadata=${output_DIR}/AAA_gene_metadata/


if [ ! -d ${output_DIR} ]; then mkdir -p ${output_DIR};
else
	echo "Warning: ${output_DIR} already exists."
fi

if [ ! -d ${output_DIR_AAA_gene_metadata} ]; then mkdir -p ${output_DIR_AAA_gene_metadata};
else
	echo "Warning: ${output_DIR_AAA_gene_metadata} already exists."
	rm -r ${output_DIR_AAA_gene_metadata}/${prefix}.K*.metadata.tsv
fi


########################################################################################################
########################                     Gene extract                       ########################
########################################################################################################

for file in `ls ${user_input}`
do
	# echo 20230812.test.MASH_Sample.K00907.tsv | sed -e 's|.*MASH_Sample\.||' -e 's|\.tsv||'
	echo ${file}
	each_KO=`echo ${file}| sed -e 's|.*MASH_Sample\.||' -e 's|\.tsv||'`
	output_extract_metadata=${output_DIR_AAA_gene_metadata}/${prefix}.${each_KO}.metadata.tsv
	echo ${each_KO}
	Rscript ${script_DIR}/M2S2.Sample_map.cmd.R \
			--in_user_KO_Sample ${user_input}/${file} \
			--in_MASH_metadata ${MASH_metadata} \
			-o ${output_extract_metadata}
done


########################################################################################################
########################            M2S2 SUCCESSFULLY FINISHED!!!               ########################
########################################################################################################
echo "M2S2 PIPELINE SUCCESSFULLY FINISHED!!!"
