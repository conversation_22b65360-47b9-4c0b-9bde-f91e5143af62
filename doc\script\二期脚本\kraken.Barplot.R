###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: <PERSON><PERSON><PERSON><PERSON>
#' @LastEditTime: 2022-11-11 23:10:14
#' @FilePath: /R/Rscript.kraken/kraken.Barplot.R
#' @Description:

suppressMessages(library(GetoptLong))
suppressMessages(library(ggplot2))
suppressMessages(library(tidyverse))
suppressMessages(library(plotly))
suppressMessages(library(htmlwidgets))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Input=s", "input Dir for Barplot Table, e.g., Barplot_Table",
  "Output=s", "output Dir for Barplot figure, e.g., Barplot_Figure",
  "verbose!","print messages"
)
################################## for循环跑每个类别 ##################################
color_theme = c(
  "#0288D1","#FF9800","#F44336","#26A69A","#FFCA28","#26C6DA",
  "#FFEE58","#009688","#8BC34A","#AB47BC","#CDDC39","#FFC107",
  "#E91E63","#9CCC65","#795548","#9C27B0","#3F51B5","#42A5F5",
  "#EF5350","#00BCD4","#66BB6A","#FF5722","#E6EE9C","#3F51B5",
  "#FFEB3B","#D4E157","#673AB7","#4CAF50","#EC407A","#9E9E9E"
)
if(! dir.exists(Output)){
  dir.create(Output)
}
for (domain in c('A', 'B', 'E', 'V')) {
  print(paste0('Domain: ',domain))
  for (taxonomy in c('P', 'C', 'O', 'F', 'G', 'S')) {
    print(paste0('Taxonomy: ',taxonomy))
    p = read.csv(paste0(Input, "/", "Barplot.", domain, ".", taxonomy, ".csv"),sep=',',header = TRUE) %>%
      {ggplot(.,aes(x=sample_name,y=Abudance,fill=Taxonomy))+
          geom_col(width=0.9)+ #柱状图每根柱子的宽度
          scale_fill_manual(labels = unique(.$Taxonomy), values = color_theme)+ # 按照物种分类学填充颜色,颜色主题是color_theme
          theme(plot.title = element_text(hjust=0.5), # title 水平调整0.5
                title=element_text(family = 'serif',size=20), # title字体为serif，字体大小20
                #axis.text.x = element_blank(),
                axis.text.x = element_text(angle = 45, hjust = 1, vjust = 1.7), # 横轴标签左倾斜45°,为了紧贴柱子,标签水平调整1,垂直调整1.7单位
                #axis.ticks = element_blank(), # 删去刻度线
                legend.title=element_blank(), # legend 的title为空
                legend.position = 'bottom', # 图例展示放于图片的下方
                legend.text = element_text(family='serif',size = 15), # legend字体为serif，字体大小15
                panel.background = element_rect(fill=NA), # 背景为空
                panel.grid = element_blank()) + # 网格线为空
          labs(x=NULL,y='Relative Abundance (%)')
      } 
      # %>% {saveWidget(ggplotly(.), file = paste0(path = Output,"Barplot.", domain, ".", taxonomy, ".html"))}
      # {ggsave(paste0("Barplot.", domain, ".", taxonomy, ".png"), .,
      #              path = Output,
      #         width = 20, height = 12)}

      saveWidget(ggplotly(p), file = paste0(Output,"/Barplot.", domain, ".", taxonomy, ".html"))
      ggsave(paste0("Barplot.", domain, ".", taxonomy, ".png"), p,path = Output,width = 20, height = 12)
  }
}
