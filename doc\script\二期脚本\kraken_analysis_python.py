#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python版本的Kraken分析脚本
从percent.csv文件生成Barplot和PCoA数据
"""

import argparse
import os

import pandas as pd
import numpy as np
from skbio import DistanceMatrix
from skbio.stats.ordination import pcoa
from sklearn.metrics.pairwise import pairwise_distances
from scipy import stats
from matplotlib.patches import Ellipse


def load_data(percent_file, group_file):
    """
    加载percent.csv文件和group.txt文件
    """
    # 读取丰度数据
    abundance_df = pd.read_csv(percent_file)
    print(f"Loaded abundance data: {abundance_df.shape}")

    # 读取分组信息
    group_df = pd.read_csv(group_file, sep='\t')
    print(f"Loaded group data: {group_df.shape}")

    return abundance_df, group_df


def prepare_abundance_matrix(abundance_df, group_df):
    """
    准备丰度矩阵，将taxon作为行，sample作为列
    """
    # 设置taxon为索引
    abundance_df = abundance_df.set_index('taxon')

    # 转置，使样本为行，taxon为列
    abundance_matrix = abundance_df.T

    # 只保留在group文件中存在的样本
    common_samples = list(set(abundance_matrix.index) & set(group_df['sample_name']))
    abundance_matrix = abundance_matrix.loc[common_samples]

    # 过滤掉全为0的列（taxon）
    abundance_matrix = abundance_matrix.loc[:, (abundance_matrix != 0).any(axis=0)]

    print(f"Final abundance matrix shape: {abundance_matrix.shape}")
    return abundance_matrix


def calculate_relative_abundance(abundance_matrix):
    """
    计算相对丰度（如果需要的话，这里假设输入已经是百分比）
    """
    # 由于输入已经是percent.csv，这里直接除以100转换为小数
    ra_matrix = abundance_matrix / 100.0
    return ra_matrix


def generate_barplot_data(ra_matrix, group_df, top_n=29):
    """
    生成条形图数据，选择丰度最高的top_n个分类
    """
    # 按总丰度排序，选择前top_n个
    total_abundance = ra_matrix.sum(axis=0).sort_values(ascending=False)

    if len(total_abundance) <= top_n:
        # 如果总数少于top_n，保留所有
        selected_taxa = total_abundance.index.tolist()
        barplot_matrix = ra_matrix[selected_taxa].copy()
    else:
        # 选择前top_n个，其余归为Others
        selected_taxa = total_abundance.head(top_n).index.tolist()
        barplot_matrix = ra_matrix[selected_taxa].copy()

        # 计算Others列
        others_abundance = ra_matrix.drop(columns=selected_taxa).sum(axis=1)
        barplot_matrix['Others'] = others_abundance

    # 转换为百分比，保留4位小数
    barplot_matrix = (barplot_matrix * 100).round(4)

    # 合并分组信息
    barplot_matrix = barplot_matrix.reset_index().rename(columns={'index': 'sample_name'})
    barplot_with_group = pd.merge(barplot_matrix, group_df, on='sample_name', how='left')

    # 转换为长格式（类似R的melt）
    id_vars = ['sample_name', 'group']
    value_vars = [col for col in barplot_with_group.columns if col not in id_vars]

    barplot_long = pd.melt(barplot_with_group,
                           id_vars=id_vars,
                           value_vars=value_vars,
                           var_name='Taxonomy',
                           value_name='Abundance')

    # 按分组排序
    barplot_long = barplot_long.sort_values(['group', 'sample_name'])

    return barplot_long, value_vars


def calculate_distance_matrix(ra_matrix, method='braycurtis'):
    """
    计算样本间距离矩阵
    """
    if method == 'braycurtis':
        # Bray-Curtis距离
        distance_matrix = pairwise_distances(ra_matrix, metric='braycurtis')
    elif method == 'jaccard':
        # Jaccard距离
        distance_matrix = pairwise_distances(ra_matrix, metric='jaccard')
    else:
        raise ValueError(f"Unsupported distance method: {method}")

    # 转换为DataFrame
    distance_df = pd.DataFrame(distance_matrix,
                               index=ra_matrix.index,
                               columns=ra_matrix.index)

    return distance_df


def generate_heatmap_data(distance_df):
    """
    生成热图数据，保留4位小数
    """
    # 对距离矩阵保留4位小数
    distance_df_rounded = distance_df.round(4)
    heatmap_data = distance_df_rounded.reset_index().rename(columns={'index': 'sample_name'})
    return heatmap_data


def generate_pcoa_data(distance_df, group_df):
    """
    生成PCoA数据 - 使用scikit-bio进行主坐标分析
    """
    import warnings

    # 创建scikit-bio的DistanceMatrix对象
    sample_ids = distance_df.index.tolist()
    dm = DistanceMatrix(distance_df.values, ids=sample_ids)

    # 执行PCoA分析，使用Cailliez校正处理负特征值（对应R脚本中的correction = "cailliez"）
    # 暂时抑制RuntimeWarning，因为我们会处理负特征值问题
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=RuntimeWarning,
                              message=".*negative eigenvalues.*")

        try:
            # 首先尝试标准PCoA
            pcoa_results = pcoa(dm, method='eigh', number_of_dimensions=2)

            # 检查是否有显著的负特征值
            eigenvalues = pcoa_results.eigvals
            if len(eigenvalues) > 0 and eigenvalues.min() < -1e-6:
                # 如果有显著负特征值，使用Cailliez校正
                raise ValueError("Significant negative eigenvalues detected")

        except (ValueError, RuntimeError):
            # 使用Cailliez校正方法
            dist_array = dm.data.copy()

            # 计算需要添加的常数（Cailliez校正）
            # 这个方法对应R中的correction = "cailliez"
            max_dist = np.max(dist_array)
            correction = max_dist * 0.001  # 使用更小的校正值

            # 确保对角线为0
            np.fill_diagonal(dist_array, 0)
            dist_array += correction

            dm_corrected = DistanceMatrix(dist_array, ids=sample_ids)
            pcoa_results = pcoa(dm_corrected, method='eigh', number_of_dimensions=2)

    # 获取前两个主坐标
    pcoa_coords = pcoa_results.samples[['PC1', 'PC2']].values

    # 修正坐标轴符号，使其与R的pcoa函数结果一致
    # 根据你的反馈，需要反转Axis.1而不是Axis.2
    pcoa_coords[:, 0] = -pcoa_coords[:, 0]  # 反转Axis.1

    # 创建PCoA结果DataFrame，保留7位小数
    pcoa_df = pd.DataFrame(pcoa_coords,
                           index=distance_df.index,
                           columns=['Axis.1', 'Axis.2']).round(7)

    # 合并分组信息
    pcoa_df = pcoa_df.reset_index().rename(columns={'index': 'sample_name'})
    pcoa_with_group = pd.merge(pcoa_df, group_df, on='sample_name', how='left')

    # 获取解释方差百分比
    explained_variance_ratio = pcoa_results.proportion_explained

    # 计算解释方差百分比标签，使用iloc避免FutureWarning
    if len(explained_variance_ratio) >= 2:
        x_label = round(explained_variance_ratio.iloc[0] * 100, 1)
        y_label = round(explained_variance_ratio.iloc[1] * 100, 1)
    else:
        x_label = round(explained_variance_ratio.iloc[0] * 100, 1) if len(explained_variance_ratio) > 0 else 0
        y_label = 0

    pcoa_labels = pd.DataFrame({
        'x_label': [x_label],
        'y_label': [y_label]
    })

    return pcoa_with_group, pcoa_labels


def generate_confidence_ellipse_data(pcoa_data, confidence=0.95, n_points=100):
    """
    生成分组置信椭圆数据

    Args:
        pcoa_data: PCoA数据，包含sample_name, group, Axis.1, Axis.2列
        confidence: 置信水平，默认0.95 (95%置信椭圆)
        n_points: 椭圆上的点数，默认100

    Returns:
        DataFrame: 包含椭圆数据的DataFrame，列为group, x, y
    """
    ellipse_data_list = []

    # 计算每个分组的置信椭圆
    for group_name in pcoa_data['group'].unique():
        group_data = pcoa_data[pcoa_data['group'] == group_name]

        # 需要至少3个样本才能计算椭圆
        if len(group_data) < 3:
            continue

        # 提取坐标
        x = group_data['Axis.1'].values
        y = group_data['Axis.2'].values

        # 计算均值
        mean_x = np.mean(x)
        mean_y = np.mean(y)

        # 计算协方差矩阵
        cov_matrix = np.cov(x, y)

        # 计算特征值和特征向量
        eigenvals, eigenvecs = np.linalg.eigh(cov_matrix)

        # 计算椭圆参数
        # 使用卡方分布的临界值
        chi2_val = stats.chi2.ppf(confidence, df=2)

        # 椭圆的半轴长度
        a = np.sqrt(chi2_val * eigenvals[1])  # 长轴
        b = np.sqrt(chi2_val * eigenvals[0])  # 短轴

        # 椭圆的旋转角度
        angle = np.arctan2(eigenvecs[1, 1], eigenvecs[0, 1])

        # 生成椭圆上的点
        t = np.linspace(0, 2 * np.pi, n_points)
        ellipse_x = a * np.cos(t)
        ellipse_y = b * np.sin(t)

        # 旋转椭圆
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)

        x_rotated = ellipse_x * cos_angle - ellipse_y * sin_angle
        y_rotated = ellipse_x * sin_angle + ellipse_y * cos_angle

        # 平移到均值位置
        x_final = x_rotated + mean_x
        y_final = y_rotated + mean_y

        # 添加到结果列表
        for i in range(n_points):
            ellipse_data_list.append({
                'group': group_name,
                'x': round(x_final[i], 7),
                'y': round(y_final[i], 7)
            })

    return pd.DataFrame(ellipse_data_list)


def main(percent_file, group_file, output_dir, domain='B', taxonomy='P', distance_method='braycurtis'):
    """
    主函数，执行完整的分析流程

    Args:
        percent_file: 输入的percent.csv文件路径
        group_file: 分组文件路径
        output_dir: 输出目录
        domain: 域标识符，默认'B'
        taxonomy: 分类级别标识符，默认'P'
        distance_method: 距离计算方法，默认'braycurtis'
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载数据
    abundance_df, group_df = load_data(percent_file, group_file)

    # 准备丰度矩阵
    abundance_matrix = prepare_abundance_matrix(abundance_df, group_df)

    # 计算相对丰度
    ra_matrix = calculate_relative_abundance(abundance_matrix)

    # 生成条形图数据
    print("Generating barplot data...")
    barplot_data, taxa_list = generate_barplot_data(ra_matrix, group_df)

    # 保存条形图数据
    barplot_file = os.path.join(output_dir, f"Barplot.{domain}.{taxonomy}.csv")
    barplot_data.to_csv(barplot_file, index=False)
    print(f"Barplot data saved to: {barplot_file}")

    # 保存条形图图例
    barlegend_data = pd.DataFrame({'Tax': taxa_list})
    barlegend_file = os.path.join(output_dir, f"Barlegend.{domain}.{taxonomy}.csv")
    barlegend_data.to_csv(barlegend_file, index=False)
    print(f"Barlegend data saved to: {barlegend_file}")

    # 计算距离矩阵
    print("Calculating distance matrix...")
    distance_df = calculate_distance_matrix(ra_matrix, distance_method)

    # 生成热图数据
    heatmap_data = generate_heatmap_data(distance_df)
    heatmap_file = os.path.join(output_dir, f"Heatmap.{domain}.{taxonomy}.{distance_method}.csv")
    heatmap_data.to_csv(heatmap_file, index=False)
    print(f"Heatmap data saved to: {heatmap_file}")

    # 生成PCoA数据
    print("Generating PCoA data...")
    pcoa_data, pcoa_labels = generate_pcoa_data(distance_df, group_df)

    pcoa_file = os.path.join(output_dir, f"PCoA.{domain}.{taxonomy}.{distance_method}.csv")
    pcoa_data.to_csv(pcoa_file, index=False)
    print(f"PCoA data saved to: {pcoa_file}")

    pcoa_label_file = os.path.join(output_dir, f"PCoA.label.{domain}.{taxonomy}.{distance_method}.csv")
    pcoa_labels.to_csv(pcoa_label_file, index=False)
    print(f"PCoA labels saved to: {pcoa_label_file}")

    # 生成置信椭圆数据
    print("Generating confidence ellipse data...")
    ellipse_data = generate_confidence_ellipse_data(pcoa_data, confidence=0.95, n_points=100)

    ellipse_file = os.path.join(output_dir, f"PCoA.ellipse.{domain}.{taxonomy}.{distance_method}.csv")
    ellipse_data.to_csv(ellipse_file, index=False)
    print(f"Confidence ellipse data saved to: {ellipse_file}")

    print("Analysis completed!")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Python版本的Kraken分析脚本')
    # parser.add_argument('--percent_file', required=True, help='输入的percent.csv文件路径')
    # parser.add_argument('--group_file', required=True, help='分组文件路径')
    # parser.add_argument('--output_dir', required=True, help='输出目录')
    # parser.add_argument('--domain', default='B', help='域标识符')
    # parser.add_argument('--taxonomy', default='P', help='分类级别标识符')
    # parser.add_argument('--distance_method', default='braycurtis', choices=['braycurtis', 'jaccard'], help='距离计算方法')
    #
    # args = parser.parse_args()

    # # 调用main函数，传入解析到的参数
    # main(
    #     percent_file=args.percent_file,
    #     group_file=args.group_file,
    #     output_dir=args.output_dir,
    #     domain=args.domain,
    #     taxonomy=args.taxonomy,
    #     distance_method=args.distance_method
    # )

    main(
        percent_file=rf'D:\data\demo\1838751471877591040\Abundance_Table\B.P.percent.csv',
        group_file=rf'D:\data\demo\1838751471877591040\group.txt',
        output_dir=rf'D:\data\demo\1838751471877591040\output', domain='B', taxonomy='P', distance_method='braycurtis'
    )
