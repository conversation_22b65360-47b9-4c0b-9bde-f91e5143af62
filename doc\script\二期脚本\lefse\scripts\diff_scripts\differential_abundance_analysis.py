#!/usr/bin/python
# -*- coding: UTF-8 -*-
'''
20200113 ddhmed
Differential abundance analysis for 16S data
'''
import sys
import getopt
import numpy as np
import pandas as pd
from scipy.stats import ranksums
import statsmodels.stats.multitest as multi


def diff_analysis(abundancefile, metadatafile, group, taxonomyfile, outputfile):
    abundance = pd.read_csv(abundancefile, index_col=0, sep='\t', header=1)
    metadata = pd.read_csv(metadatafile, index_col=0, sep='\t')
    taxonomy = pd.read_csv(taxonomyfile, index_col=0, sep='\t')
    class_set = list(set(metadata[group][1:]))  # 第一行为数据类型，一般两类：正常/疾病，以防有多类，两两比较
    result = []
    for obj in abundance.index:
        row_res = [taxonomy.loc[obj, 'Taxon']]
        abuns = [abundance.loc[obj, metadata.loc[metadata[group] == i, :].index] for i in class_set]
        row_res.extend([i.mean() for i in abuns])
        for i in range(len(class_set) - 1):
            for j in range(i + 1, len(class_set)):
                f, p_value = ranksums(abuns[i], abuns[j])
                foldchange = abuns[i].mean() / abuns[j].mean() if abuns[j].mean() != 0 else np.inf
                row_res.extend([foldchange, p_value])
        result.append(row_res)
    index = abundance.index
    columns = ['Taxon']
    columns.extend([i + '_mean' for i in class_set])
    add_fdr_columns = columns[:]
    for i in range(len(class_set) - 1):
        for j in range(i + 1, len(class_set)):
            columns.extend(
                [class_set[i] + '_vs_' + class_set[j] + '_foldchange', class_set[i] + '_vs_' + class_set[j] + '_pval'])
            add_fdr_columns.extend(
                [class_set[i] + '_vs_' + class_set[j] + '_foldchange', class_set[i] + '_vs_' + class_set[j] + '_pval',
                 class_set[i] + '_vs_' + class_set[j] + '_fdr'])
    result = pd.DataFrame(result, columns=columns, index=index)
    for c in columns:
        if '_pval' in c:
            result[c.replace('_pval', '_fdr')] = multi.multipletests(result[c], method='fdr_bh')[1]
    result = result.loc[:, add_fdr_columns]
    result.to_csv(outputfile, sep='\t')
    print('Done!')


def main(argv):
    try:
        opts, args = getopt.getopt(argv, "ha:m:c:t:o:",
                                   ["abundancefile=", "metadatafile=", "group=", "taxonomyfile=", "outputfile="])
    except getopt.GetoptError:
        print('''differential_abundance_analysis.py 
              -a <abundance file> 
              -m <metadata file>
              -c <group column>
              -t <taxonomy file>
              -o <outputfile>''')
        sys.exit(2)
    for opt, arg in opts:
        if opt == '-h':
            print('''differential_abundance_analysis.py 
              -a <abundance file> 
              -m <metadata file>
              -c <group column>
              -t <taxonomy file>
              -o <outputfile>''')
            sys.exit()
        elif opt in ("-a", "--abundancefile"):
            abundancefile = arg
        elif opt in ("-m", "--metadatafile"):
            metadatafile = arg
        elif opt in ("-c", "--group"):
            group = arg
        elif opt in ("-t", "--taxonomyfile"):
            taxonomyfile = arg
        elif opt in ("-o", "--outputfile"):
            outputfile = arg
    print('输入的丰度文件为：', abundancefile)
    print('输入的meta文件为：', metadatafile)
    print('输入的分组标签为：', group)
    print('输入的物种注释文件为：', taxonomyfile)
    print('输出的文件为：', outputfile)
    diff_analysis(abundancefile, metadatafile, group, taxonomyfile, outputfile)


if __name__ == '__main__':
    main(sys.argv[1:])
