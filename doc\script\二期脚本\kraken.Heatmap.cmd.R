###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-04-07 15:53:26
#' @FilePath: /liliuyang/R/Rscript.MASH/20240407/kraken.Heatmap.cmd.R
#' @Description:

# setwd('/data1/liliuyang/ColdSeep/202309/Rplot')

suppressMessages(library(GetoptLong))
suppressMessages(library(pheatmap))
suppressMessages(library(tidyverse))

# setwd("E:/deepsea/R/metadata.20221101/R.5group")
rm(list = ls(all.names = TRUE))

GetoptLong(
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Group_color=s", "group table, e.g., group_color.txt with two coloumn: Group and Color",  "Input=s", "input Dir for Heatmap Table, e.g., Heatmap_Table",
  "Output=s", "output Dir for Heatmap figure, e.g., Heatmap_Figure",
  "verbose!","print messages"
)

#######

save_pheatmap_pdf <- function(x, filename, width=7, height=7) {
  stopifnot(!missing(x))
  stopifnot(!missing(filename))
  pdf(filename, width=width, height=height)
  grid::grid.newpage()
  grid::grid.draw(x$gtable)
  dev.off()
}


draw_heatmap=function(data = plot.heatmap, group = group, Group_color = Group_color_data) {
  #  'sub_Sample_KO' 是热图数据
  tryCatch({
    group_each_map = group %>%
      {.[.$sample_name %in% unique(colnames(data)), ]}
    levels_group = sort(unique(group_each_map$group))
    group_each_map$group=factor(group_each_map$group,
                                levels = levels_group)
    anno_row = group_each_map %>%
      {subset(., select=get('group'))}

    Group_color_each_map =  Group_color %>%
      {.[.$Group %in% unique(group_each_map$group), ]} %>%
      {.[levels_group, ]} %>%
      {mutate(., group = .$Group)} %>%
      {subset(., select=get('Color'))}
    ann_colors = Group_color_each_map$Color
    names(ann_colors) = rownames(Group_color_each_map)
    ann_colors = list(group = ann_colors)

    p1 = pheatmap::pheatmap(data,
                    method = 'average',
                    cutree_rows = length(unique(group_each_map$group)),
                    cutree_cols = length(unique(group_each_map$group)),
                    na_col='grey',
                    cluster_rows = T,
                    cluster_cols = T,###是否对行列聚类
                    show_rownames = T,show_colnames = T,###是否显示行名列名
                    cellwidth = 7,cellheight = 6,###固定每个cell的宽和高
                    display_numbers = F,####是否显示每个单元格的值
                    fontsize_row = 6,
                    fontsize_col	= 5,
                    angle_col = 45,
                    annotation_col = anno_row,
                    annotation_colors = ann_colors,
                    annotation_row = anno_row,
                    silent = TRUE
      )
    return(p1)
  }, error = function(e) {
    message("Error in pheatmap: ", e)
    min_val <- min(data)
    max_val <- max(data,1e-10)
    # 创建 breaks 向量，其中的值从最小值到最大值均匀分布，且0对应于白色
    breaks <- c(min_val, max_val)
    colors <- rep("blue", times = 100)
    p1 = pheatmap::pheatmap(data,
                    method = 'average',
                    na_col='grey',
                    breaks = breaks,
                    cluster_rows = F,
                    cluster_cols = F,###是否对行列聚类
                    show_rownames = T,show_colnames = T,###是否显示行名列名
                    cellwidth = 7,cellheight = 6,###固定每个cell的宽和高
                    display_numbers = F,####是否显示每个单元格的值
                    fontsize_row = 6,
                    fontsize_col	= 5,
                    angle_col = 45,
                    silent = TRUE
      )
    return(p1)
  })
}

##########################      Data input      ##########################

#Group = 'Input_Table/group.tsv'
group = read.csv(Group,sep='\t',header = TRUE,row.names = 1,check.names = F)
group$sample_name <- rownames(group) #添加sample_name列

#Group_color = 'Input_Table/group_color.tsv'
Group_color_data = read.csv(Group_color, sep='\t',header = TRUE, col.names = c('Group', 'Color'),
                             comment.char="", stringsAsFactors = FALSE,check.names = F) %>%
  {.[.$Group %in% unique(group$group), ]}
rownames(Group_color_data) = Group_color_data$Group

#ann_colors = list(
#  group = c(GroupA = "#377EB8", GroupB = "#FF7F00", GroupC = "#E41A1C", GroupD = "#4DAF4A", GroupE = "#984EA3")
#)

if(! dir.exists(Output)){
  dir.create(Output)
}
################################## for循环跑每个类别 ##################################
for (domain in c('A', 'B', 'E', 'V')) {
  print(paste0('Domain: ',domain))
  for (Beta_level in c('P', 'C', 'O', 'F', 'G', 'S')) {
    print(paste0('Beta_level: ',Beta_level))
    for (beta in c('bray', 'jaccard')) {
      print(paste0('Method: ',beta))
        ## 热图
        file_name = paste0(Input, "/", "Heatmap.", domain, ".", Beta_level, ".", beta, ".csv")
        if(file.exists(file_name)){
          plot.heatmap = read.csv(file_name,sep=',',header = TRUE, row.names = 1,check.names = F)

          num_sample = length(unique(colnames(plot.heatmap)))
          print(paste0("num of columns: ", num_sample))
          # 按照线性关系width=3+0.5*num_group
          figure_width=5+0.09*num_sample
          if (figure_width > 49){
            figure_width = 49
          }

          num_KO = length(unique(rownames(plot.heatmap)))
          print(paste0("num of rows: ", num_KO))
          # 按照线性关系width=3+0.5*num_group
          figure_height=5+0.07*num_KO
          if (figure_height > 49){
            figure_height = 49
          }

          p1 = draw_heatmap(data = plot.heatmap,
                            group = group,
                            Group_color = Group_color_data)

          save_pheatmap_pdf(p1, paste0(Output, "/Heatmap.", domain, ".", Beta_level, ".", beta, ".pdf"),width = figure_width, height = figure_height)
      }
    }
  }
}
