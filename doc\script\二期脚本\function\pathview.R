#!/usr/bin/env Rscript
###
#' @Author: LiQiang
#' @Date: 2023-08-13
#' @LastEditors: LiQiang
#' @LastEditTime: 2023-08-17
#' @FilePath: /bdp-picb/bioinfo/liqiang/project/metagenome/mash-ocean/pathview/scripts/pathview.R
#' @Description: 
###
library(pathview)
data("demo.paths")

if (!suppressWarnings(suppressMessages(require("optparse", character.only = TRUE, quietly = TRUE, warn.conflicts = FALSE)))) {
    install.packages("optparse", repos=site)
    require("optparse",character.only=T)
}

if (TRUE){
  option_list = list(
    make_option(c("-k", "--ko_file"), type="character", default="demo.KO_depth.tsv",
                help="ko abundance file [default %default]"),
    make_option(c("-o", "--prefix"), type="character", default="demo",
                help="output file prefix [default %default]"),
    make_option(c("-p", "--pathway"), type="character", default="cns",
                help="pathway id to plot [default %default]"),
    make_option(c("-d", "--outdir"), type="character",
                help="outdir to save [default %default]")
  )
  opts = parse_args(OptionParser(option_list=option_list))
}



#ko_abund <- read.table("DRR090564.KO_depth.tsv", header=T, row.names = 1,sep="\t")
out_path <- opts$outdir
setwd(out_path)
#setwd("/bdp-picb/bioinfo/liqiang/project/metagenome/mash-ocean/pathview/test-final/")
ko_abund <- read.table(opts$ko_file, header=T, row.names = 1,sep="\t")
ko_abund_trans <- log10(ko_abund+1)
top = ceiling(max(ko_abund_trans))
if(opts$pathway == "cns"){
   for (path_id in c("00190","00195","00196","00680","00710","00720","00910","00920")){
	pv.out <- pathview(gene.data = ko_abund,
                   cpd.data = NULL,
                   pathway.id = path_id,
                   species = "ko",
                   out.suffix = opts$prefix,
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene =1000,cpd = 1),		
                   both.dirs = list(gene = F, cpd = T),kegg.native = TRUE) 
   }

   for (path_id in c("00190","00195","00196","00680","00710","00720","00910","00920")){
        pv.out <- pathview(gene.data = ko_abund_trans,
                   cpd.data = NULL,
                   pathway.id = path_id,
                   species = "ko",
                   out.suffix = paste0(opts$prefix,".log"),
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene = top,cpd = 1),
                   both.dirs = list(gene = F, cpd = T),kegg.native = TRUE)
   }
}else{
        pv.out <- pathview(gene.data = ko_abund_trans,
                   cpd.data = NULL,
                   pathway.id = opts$pathway,
                   species = "ko",
                   out.suffix = paste0(opts$prefix,".log"),
                   discrete = list(gene = F,cpd = F),
                   bins = list(gene = 10,cpd = 10),
                   limit = list(gene = top,cpd = 1),
                   both.dirs = list(gene = F, cpd = T),kegg.native = TRUE)
}

###  multiple samples   ###
#Task_id = "";
#pv.out <- pathview(gene.data = gse16873.d[, 1:3]
#                   cpd.data = NULL,
#                   pathway.id = path_id,
#                   species = "ko",
#                   out.suffix = Task_id,
#                   discrete = list(gene = T,cpd = F),
#                   bins = list(gene = 1,cpd = 10),
#                   limit = list(gene = 1, cpd = 1),both.dirs = list(gene = F, cpd = T),kegg.native = TRUE)
#}

