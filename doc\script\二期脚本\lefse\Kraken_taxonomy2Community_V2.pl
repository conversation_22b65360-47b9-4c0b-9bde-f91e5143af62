#! /usr/bin/perl
if(@ARGV<5){
	print "Kraken_taxonomy2Community.pl kraken_merge_file sample_list outdir Domain metadata\n";
	exit;
}
my $kraken = shift;
my $sample = shift;
my $outdir = shift;
my $domain = shift;
my $metadata = shift;

my %rename;
my @raw_names;
my @samples;
open(SAMPLE,"<$sample") or die;
<SAMPLE>; 
while(<SAMPLE>){
	chomp $_;
	my @line = split /\t/,$_;
	$rename{$line[0]} = $line[0];
	#$raw_names[@raw_names] = $line[0];
	$samples[@samples] = $line[0];
}
close(SAMPLE);

my %groups;
open(GROUP,"<$metadata") or die;
my $group_header = <GROUP>;
chomp $group_header;
while(<GROUP>){
	chomp $_;
	my @line = split /\t/,$_;
	$groups{$line[0]} = $line[1];
}
close(GROUP);


`mkdir $outdir/$domain`;
open(INCLUDE,">$outdir/$domain/include.xls") or die;
open(EXCLUDE,">$outdir/$domain/exclude.xls") or die;
open(GROUP2,">$outdir/$domain/group.txt") or die;
print GROUP2 "$group_header\n";

my %percent;
my %percent2;
my %level;
my %includes;
my @sample_include;
open(KRAKEN,"<$kraken") or die;
my $header = <KRAKEN>;
if($header =~ /^#/){
	$header = <KRAKEN>;
}
chomp $header;
my @headers = split /\t/,$header;
for(my $i=2;$i<@headers;$i++){
	#$headers[$i] =~ /(\S+)\.taxonomy/;
	#$headers[$i] = $headers[$i];
}
while(<KRAKEN>){
	chomp $_;
	my @line = split /\t/,$_;
	if($line[2] ne $domain){
		next;
	}
	if($line[1] eq "D"){
		for(my $i=3;$i<@line;$i++){
			if($line[$i] >0){
				print INCLUDE "$headers[$i]\t$headers[$i]\n";
				$raw_names[@raw_names] = $headers[$i];
				$sample_include[@sample_include] = $headers[$i];
				$includes{$headers[$i]} =1;
				#print GROUP2 "$headers[$i]\t$groups{$headers[$i]}\n";
			}else{
				print EXCLUDE "$headers[$i]\t$headers[$i]\n";
			}
			#print "$headers[$i]\t$tmp\t$line[$i]\n";
		}
	}
	#$line[0] =~ /([^|])__([^|]+)$/;
	$line[0] =~ s/ /_/g;
	my $tmp = $line[1]."||".$line[0];
	$level{$tmp} = $line[1];
	#print "$tmp\t$line[1]\n";
	###  p__Actinobacteria|c__Actinobacteria 
	###  same taxonomy;
	#my $taxonomy = $2;
	for(my $i=2;$i<@line;$i++){
		$percent{$headers[$i]}{$tmp} = $line[$i];
		$percent2{$headers[$i]}{$tmp} = $line[$i]/100;
		#print "$headers[$i]\t$tmp\t$line[$i]\n";
	}	
}
close(KRAKEN);
close(INCLUDE);
close(EXCLUDE);


open(GROUP,"<$metadata") or die;
my $group_header = <GROUP>;
chomp $group_header;
while(<GROUP>){
	chomp $_;
	my @line = split /\t/,$_;
	if($includes{$line[0]} ==1){
		print GROUP2 "$line[0]\t$groups{$line[0]}\n";	
	}
}
close(GROUP);
close(GROUP2);

open(PHYLUM,">$outdir/$domain/phylum.xls") or die;
open(CLASS,">$outdir/$domain/class.xls") or die;
open(ORDER,">$outdir/$domain/order.xls") or die;
open(FAMILY,">$outdir/$domain/family.xls") or die;
open(GENUS,">$outdir/$domain/genus.xls") or die;
open(SPECIES,">$outdir/$domain/species.xls") or die;

open(PHYLUM2,">$outdir/$domain/phylum.percents.xls") or die;
open(CLASS2,">$outdir/$domain/class.percents.xls") or die;
open(ORDER2,">$outdir/$domain/order.percents.xls") or die;
open(FAMILY2,">$outdir/$domain/family.percents.xls") or die;
open(GENUS2,">$outdir/$domain/genus.percents.xls") or die;
open(SPECIES2,">$outdir/$domain/species.percents.xls") or die;


my $head_join = join("\t",@sample_include);
print PHYLUM "Taxon\t$head_join\n";
print CLASS "Taxon\t$head_join\n";
print ORDER "Taxon\t$head_join\n";
print FAMILY "Taxon\t$head_join\n";
print GENUS "Taxon\t$head_join\n";
print SPECIES "Taxon\t$head_join\n";

print PHYLUM2 "# Constructed from biom file\n";
print PHYLUM2 "Taxon\t$head_join\n";
print CLASS2 "# Constructed from biom file\n";
print CLASS2 "Taxon\t$head_join\n";
print ORDER2 "# Constructed from biom file\n";
print ORDER2 "Taxon\t$head_join\n";
print FAMILY2 "# Constructed from biom file\n";
print FAMILY2 "Taxon\t$head_join\n";
print GENUS2 "# Constructed from biom file\n";
print GENUS2 "Taxon\t$head_join\n";
print SPECIES2 "# Constructed from biom file\n";
print SPECIES2 "Taxon\t$head_join\n";

foreach my $key (sort {$a cmp $b} keys %level) {
	if($level{$key} eq "P"){
		$key =~ /(\S)\|\|(\S+)/;
		print PHYLUM "$2\t";
		print PHYLUM2 "$2\t";
		for(my $j=0;$j<@raw_names-1;$j++){
			print PHYLUM "$percent{$raw_names[$j]}{$key}\t";
			print PHYLUM2 "$percent2{$raw_names[$j]}{$key}\t"; 
		} 
		
		print PHYLUM "$percent{$raw_names[@raw_names-1]}{$key}";
		print PHYLUM "\n";
		print PHYLUM2 "$percent2{$raw_names[@raw_names-1]}{$key}";
                print PHYLUM2 "\n";

	}elsif($level{$key} eq "C"){
                $key =~ /(\S)\|\|(\S+)/;
		print CLASS "$2\t";
		print CLASS2 "$2\t";
                for(my $j=0;$j<@raw_names-1;$j++){
                        print CLASS "$percent{$raw_names[$j]}{$key}\t";
			print CLASS2 "$percent2{$raw_names[$j]}{$key}\t";
                }
                print CLASS "$percent{$raw_names[@raw_names-1]}{$key}\n";
		print CLASS2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
	}elsif($level{$key} eq "O"){
                $key =~ /(\S)\|\|(\S+)/;
                print ORDER "$2\t";
		print ORDER2 "$2\t";
                for(my $j=0;$j<@raw_names-1;$j++){
                        print ORDER "$percent{$raw_names[$j]}{$key}\t";
			print ORDER2 "$percent2{$raw_names[$j]}{$key}\t";
                }
                print ORDER "$percent{$raw_names[@raw_names-1]}{$key}\n";
		print ORDER2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
	}elsif($level{$key} eq "F"){
                $key =~ /(\S)\|\|(\S+)/;
                print FAMILY "$2\t";
		print FAMILY2 "$2\t";
                for(my $j=0;$j<@raw_names-1;$j++){
                        print FAMILY "$percent{$raw_names[$j]}{$key}\t";
			print FAMILY2 "$percent2{$raw_names[$j]}{$key}\t";
                }
                print FAMILY "$percent{$raw_names[@raw_names-1]}{$key}\n";
		print FAMILY2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
	}elsif($level{$key} eq "G"){
                $key =~ /(\S)\|\|(\S+)/;
                print GENUS "$2\t";
		print GENUS2 "$2\t";
                for(my $j=0;$j<@raw_names-1;$j++){
                        print GENUS "$percent{$raw_names[$j]}{$key}\t";
			print GENUS2 "$percent2{$raw_names[$j]}{$key}\t";
                }
                print GENUS "$percent{$raw_names[@raw_names-1]}{$key}\n";
		print GENUS2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
	}elsif($level{$key} eq "S"){
                $key =~ /(\S)\|\|(\S+)/;
                print SPECIES "$2\t";
		print SPECIES2 "$2\t";
                for(my $j=0;$j<@raw_names-1;$j++){
                        print SPECIES "$percent{$raw_names[$j]}{$key}\t";
			print SPECIES2 "$percent2{$raw_names[$j]}{$key}\t";
                }
                print SPECIES "$percent{$raw_names[@raw_names-1]}{$key}\n";
		print SPECIES2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
	}
}

