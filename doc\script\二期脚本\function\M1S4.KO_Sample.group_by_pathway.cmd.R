###
#' @Date: 2022-11-02 23:40:20
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2024-03-21 14:34:33
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v7/M1S4.KO_Sample.group_by_pathway.cmd.R
#' @Description: transfer the count to relative abundance

suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("reshape2","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_KO_pathway_info=s", "in_KO_pathway_info",
  "in_KO_Sample_wide=s", "in_KO_Sample_wide",
  "out_DIR_KO_Sample_by_pathway=s", "output directory",
  "prefix=s", "prefix",
  "verbose!","print messages"
)
##############add_rows_if_not_exists##############
add_rows_if_not_exists <- function(module_abundance, add_rows = c("K14126","K14128","K14127")) {
  for (row_name in add_rows) {
    if (!(row_name %in% rownames(module_abundance))) {
      module_abundance[row_name, ] <- 0
    }
  }
  return(module_abundance)
}
##########################      pathway infor input      ##########################
# 'MASH-KEGG_Energy metabolism.tsv'
pathway_infor = read.csv(in_KO_pathway_info, sep='\t',header = TRUE, check.names = F)
# %>%  {mutate(., KO_Symbol = paste0(Orthology_Entry, '; ', Orthology_Symbol))}

##########################      Sample-KO depth input      ##########################
# 'MASH.20230813.test.KO_Sample.wide.tsv' relative abundance * 100,0000
KO_Sample = read.csv(in_KO_Sample_wide, sep='\t',header = TRUE, row.names = 1, check.names = F) %>%
  {round(log10((.) + 1), 4)} %>%
  {mutate(., Orthology_Entry = rownames(.))} %>%
  {.[rownames(.) %in% unique(pathway_infor$Orthology_Entry),,drop = F]}

# 'Photosynthesis'
# unique(pathway_infor[,'Pathway_Name'])

for (pathway in unique(pathway_infor[,'Pathway_Name']) ) {
  pathway_scale = gsub(' ', '_', pathway)
  out_file = paste0(out_DIR_KO_Sample_by_pathway, '/', prefix, ".KO_Sample.wide.", pathway_scale, ".tsv")
  error_file = paste0(out_DIR_KO_Sample_by_pathway, '/', prefix, ".KO_Sample.error.", pathway_scale, ".tsv")

  each_pathway_infor = pathway_infor %>%
    {.[(.$Pathway_Name) %in% pathway, c('KO_Symbol', 'Orthology_Entry'),drop = F]} %>%
      {unique(.)}
  sub_KO_Sample = KO_Sample %>%
    {.[rownames(.) %in% unique(each_pathway_infor$Orthology_Entry),]} %>%
    {add_rows_if_not_exists(., add_rows = unique(each_pathway_infor$Orthology_Entry))} %>%
    {mutate(., Orthology_Entry = rownames(.))}
    # KO 是0的也加上

  if (length(sub_KO_Sample$Orthology_Entry)>0){
    print(paste0('Running pathway: ', pathway))
    sub_KO_Sample_pathway = each_pathway_infor %>%
      {merge(., sub_KO_Sample, by = 'Orthology_Entry', all.y = T)} %>%
      {select(., -Orthology_Entry)} %>%
      {write.table(.,
                  out_file, sep = "\t", row.names = F, col.names = T,quote = FALSE,  fileEncoding="UTF-8")}

  } else {
    print(paste0('Error: ', pathway, ' was not detected!!!'))
    file.create(error_file)
  }
}
