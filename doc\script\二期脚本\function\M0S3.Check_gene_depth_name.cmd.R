###
#' @Date: 2023-08-18 22:02:05
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-30 11:33:28
#' @FilePath: /liliuyang/R/Rscript.MASH/202308-v4/M0S3.Check_gene_depth_name.cmd.R
#' @Description:
###


suppressMessages(library(GetoptLong))
rm(list = ls(all.names = TRUE))
packages=c("dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_orf_name=s", "orf_KO mapping file",
  "depth_bbmap_in_file=s", "orf depth_bbmap file",
  "sample_name=s", "file store the sample name",
  "out_DIR=s", "output directory",
  "verbose!","print messages"
)

# sample name
sample = sample_name

# 'MASH/M1S2_output/MASH.20230818.test.KO_Sample.wide.tsv'
contig1000_depth <- read.csv(depth_bbmap_in_file, sep = '\t',
                    quote='', header = T, col.names = c('Contig','Avg_fold'))

check_orf_name = in_orf_name %>%
  read.csv(., sep = '\t', quote='', header = F, col.names = c('orf'))

if(! dir.exists(out_DIR)){
  dir.create(out_DIR, recursive = T)
}

sample_error = paste0(out_DIR, '/', sample, '.error.tsv')
mapping_error = paste0(out_DIR, '/', sample, '.mapping_error.tsv')


if(file.exists(sample_error)){
  file.remove(sample_error)
}

if(file.exists(mapping_error)){
  file.remove(mapping_error)
}

if (length(check_orf_name$orf)>0){
  length_insect_orf = check_orf_name$orf %>%
    {length(.[(.) %in% contig1000_depth$Contig])}
  if (length(check_orf_name$orf) == length_insect_orf){
    print("The Gene Abundance file is orf abundance!")
  } else {
    check_orf_name = check_orf_name %>%
      {mutate(., Contig = sapply(as.character(.$orf),
                                function(x) unlist(strsplit(x, "_\\d*$"))[1]))}
    length_insect_contig = check_orf_name$Contig %>%
      {length(.[(.) %in% contig1000_depth$Contig])}
    if (length(check_orf_name$Contig) == length_insect_contig){
      print("The Gene Abundance file is contig mean abundance!")
    } else {
      file.create(mapping_error)
      stop("Please check the mapping relationship between the Gene ID file and Gene Abundance file!")
    }
  }
} else {
  file.create(sample_error)
}

print("M0S3.Check_gene_depth_name.cmd.R finished!")
