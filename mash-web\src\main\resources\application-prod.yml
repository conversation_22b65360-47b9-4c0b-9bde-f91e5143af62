spring:
  data:
    mongodb:
      uri: *********************************************************
      auto-index-creation: true
  elasticsearch:
    uris: http://mash-es:9200
    socket-keep-alive: true
  ai:
    embedding:
      transformer:
        enabled: true
        onnx:
          model-uri: file:/data/mash/sentence-transformers/bce-embedding-base_v1-onnx/model.onnx
          model-output-name: token_embeddings
        tokenizer:
          uri: file:/data/mash/sentence-transformers/bce-embedding-base_v1-onnx/tokenizer.json
          options:
            maxLength: 512
            modelMaxLength: 512


logging:
  level:
    org.springframework.data.elasticsearch: warn
    org.elasticsearch.client: warn

app:
  data-home: "/data/mash"
  # 统计页面验证token
  stat-token: "kZ7mXbLQwE92Rt"
  # Dify api请求地址
  dify-url: "https://www.biosino.org/dify/v1"
  # Dify api密钥
  dify-token: "app-LPZxTYAyycY79PmhBf95fF21"
  # milvus知识库请求地址
  knowledge-url: "http://10.16.16.189:32080/zsk-api"
  # milvus知识库名称
  knowledge-id: "mash_doc_milvus"
  # milvus知识库密钥
  knowledge-token: "lrj_test_token_2025"
  # 专家问答收件人管理员邮箱
  admin-email: "<EMAIL>"
  # 专家问答邮件抄送邮箱
  cc-emails:
    - "<EMAIL>"
  node:
    endpoint: https://www.biosino.org/node/api/api
    api-token: d08779ddd3a944aa17ad0f20fb256547
    get-experiment-metadata-url: ${app.node.endpoint}/metadata/experiment/list
    save-request-data-url: ${app.node.endpoint}/node/resourceAuthorize/saveBatch
    get-metadata-url: ${app.node.endpoint}/metadata/{}/list
  antnest:
    admin-email: <EMAIL>
