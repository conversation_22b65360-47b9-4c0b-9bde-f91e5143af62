<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <h3 class="mt-0">
              <svg-icon icon-class="filter" class-name="filter"></svg-icon>
              Filter
            </h3>
            <el-divider class="mt-1"></el-divider>

            <el-form
              :model="form"
              label-width="auto"
              style="max-width: 600px"
              label-position="top"
            >
              <el-form-item label="Water Body Name">
                <el-autocomplete
                  v-model="form.waterBodyName"
                  :fetch-suggestions="querySearch"
                  clearable
                  class="w-100"
                  placeholder="Please Input"
                />
              </el-form-item>
              <el-form-item label="Country">
                <el-autocomplete
                  v-model="form.country"
                  :fetch-suggestions="queryCountrySearch"
                  clearable
                  class="w-100"
                  placeholder="Please Input"
                />
              </el-form-item>
              <el-form-item label="Longitude">
                <el-slider
                  v-model="form.sliderLongitude"
                  range
                  :max="180.0"
                  :min="-180.0"
                  step="0.01"
                  placement="right"
                />
                <div class="d-flex align-items-center justify-center">
                  <el-input v-model="longitudeTo" style="width: 130px" />
                  <span class="mr-05 ml-05">~</span>
                  <el-input v-model="longitudeFrom" style="width: 130px" />
                </div>
              </el-form-item>

              <el-form-item label="Latitude">
                <el-slider
                  v-model="form.sliderLatitude"
                  range
                  :max="90"
                  :min="-90"
                  step="0.01"
                  placement="right"
                />
                <div class="d-flex align-items-center justify-center">
                  <el-input v-model="latitudeTo" style="width: 130px" />
                  <span class="mr-05 ml-05">~</span>
                  <el-input v-model="latitudeFrom" style="width: 130px" />
                </div>
              </el-form-item>
              <el-form-item label="Hydrosphere Type">
                <el-cascader
                  :props="props"
                  :options="hydrosphereTypeOpt"
                  :show-all-levels="false"
                  class="w-100"
                />
              </el-form-item>
              <el-form-item label="Critical Zone">
                <el-select
                  v-model="form.criticalZone"
                  filterable
                  placeholder="Select"
                >
                  <el-option label="IN" value="IN" />
                  <el-option label="OUT" value="OUT" />
                </el-select>
              </el-form-item>
              <el-form-item label="Feature Dataset ">
                <el-select
                  v-model="form.dataset"
                  filterable
                  placeholder="Select"
                >
                  <el-option label="MASH-Lake" value="MASH-Lake" />
                  <el-option label="MASH-Ocean" value="MASH-Ocean" />
                  <el-option label="MASH-Chinasea" value="MASH-Chinasea" />
                  <el-option label="MASH-China" value="MASH-China" />
                  <el-option label="MEER" value="MEER" />
                </el-select>
              </el-form-item>
              <el-form-item label="Omics Type">
                <el-select
                  v-model="form.omicsType"
                  filterable
                  placeholder="Select"
                >
                  <el-option label="Metagenomic" value="Metagenomic" />
                  <el-option label="Genomic" value="GENOMIC" />
                  <el-option
                    label="Metatranscriptomic"
                    value="METATRANSCRIPTOMIC"
                  />
                  <el-option label="Transcriptomic" value="TRANSCRIPTOMIC" />
                  <el-option label="Viral RNA" value="VIRAL RNA" />
                  <el-option
                    label="Genomic single cell"
                    value="GENOMIC SINGLE CELL"
                  />
                  <el-option label="Other" value="OTHER" />
                </el-select>
              </el-form-item>
              <el-form-item label="Technology Type">
                <el-select
                  v-model="form.technologyType"
                  filterable
                  placeholder="Select"
                >
                  <el-option label="WGS" value="WGS" />
                  <el-option label="WGA" value="WGA" />
                  <el-option label="AMPLICON" value="AMPLICON" />
                  <el-option label="RNA-Seq" value="RNA-Seq" />
                  <el-option label="Bisulfite-Seq" value="Bisulfite-Seq" />
                  <el-option label="RAD-Seq" value="RAD-Seq" />
                  <el-option label="Other" value="Other" />
                  <el-option label="...." value="...." />
                </el-select>
              </el-form-item>
              <el-form-item label="Sampling Substrate">
                <el-select
                  v-model="form.sampSubstrate"
                  filterable
                  placeholder="Select"
                >
                  <el-option label="Water" value="Water" />
                  <el-option label="Sediment" value="Sediment" />
                  <el-option label="Soil" value="Soil" />
                  <el-option label="Air" value="Air" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  class="w-100 filter-search mt-1"
                  icon="Search"
                  >Search
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="card pos-relative">
            <div class="d-flex justify-space-between">
              <h3 class="mb-0 mt-0">Sample List</h3>
            </div>
            <el-divider class="mt-1"></el-divider>
            <el-table
              ref="table"
              height="680"
              tooltip-effect="dark"
              :data="dataTable"
              :header-cell-style="{
                backgroundColor: '#F1F5F9',
                color: '#333333',
                fontWeight: 700,
              }"
              border
              :stripe="true"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column
                label="Run ID"
                prop="runId"
                width="130"
              ></el-table-column>
              <el-table-column
                label="Bioproject ID"
                prop="projectId"
                width="140"
              ></el-table-column>
              <el-table-column
                label="Omics Type"
                prop="omicsType"
                width="150"
              ></el-table-column>
              <el-table-column
                label="Technology Type"
                prop="technologyType"
                width="160"
              ></el-table-column>
              <el-table-column
                label="Lat Lon"
                prop="latLon"
                width="160"
              ></el-table-column>
              <el-table-column
                label="Hydrosphere Type"
                prop="waterType"
                width="180"
              ></el-table-column>
              <el-table-column
                label="Critical Zone"
                prop="criticalZone"
                width="130"
              ></el-table-column>
              <el-table-column
                label="Sampling Substrate"
                prop="sampSubstrate"
                width="180"
              ></el-table-column>
              <el-table-column
                label="Country"
                prop="country"
                width="120"
              ></el-table-column>
              <el-table-column
                label="Water Body Name"
                prop="waterName"
                show-overflow-tooltip
                width="200"
              ></el-table-column>
              <el-table-column
                label="Feature Dataset"
                prop="featureDataset"
                width="150"
              ></el-table-column>
            </el-table>

            <div class="pos-relative">
              <div class="select-samp">
                <el-button type="warning" class="mr-0">Select All</el-button>
                <el-button type="danger">Clean All</el-button>
              </div>
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                class="mb-1 mt-2 justify-center"
                :page-sizes="[100, 200, 300, 400]"
                layout="total, sizes, prev, pager, next"
                :total="dataTable.length"
              />
            </div>

            <div class="bg-gray selected-samp mt-1">
              <div class="d-flex row-gap-10 flex-wrap">
                <span class="font-600">Selected Samples:</span>
                <el-tag
                  v-for="item in selectedTags"
                  :key="item.label"
                  type="primary"
                  effect="light"
                  round
                  closable
                  size="default"
                  @close="handleClose(item)"
                >
                  {{ item }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <el-form :model="form" label-width="auto" label-position="top">
              <el-form-item label="Genes Input Type">
                <el-radio-group v-model="form.geneInputType">
                  <el-radio value="Gene Name">Gene Name</el-radio>
                  <el-radio value="Orthology Entry">Orthology Entry</el-radio>
                  <el-radio value="Gene Sequence">Gene Sequence</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="Species Input Type">
                <el-radio-group
                  v-model="form.speciesType"
                  aria-label="item label position"
                >
                  <el-radio value="Species Name">Species Name</el-radio>
                  <el-radio value="Genome Sequence">Genome Sequence</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="Gene Name">
                <el-select v-model="form.geneName" filterable multiple>
                  <el-option value="K01689; ENO,eno; enolase [EC:********]"
                    >K01689; ENO,eno; enolase [EC:********]
                  </el-option>
                  <el-option
                    value="K01681; ACO,acnA; aconitate hydratase [EC:*******]"
                    >K01681; ACO,acnA; aconitate hydratase [EC:*******]
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Input Type">
                <el-radio-group
                  v-model="form.inputType"
                  aria-label="item label position"
                >
                  <el-radio value="KO-Sample">KO-Sample</el-radio>
                  <el-radio value="Gene-KO">Gene-KO</el-radio>
                  <el-radio value="Sequence">Sequence</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="Upload KO-Sample Table (s) ">
                <el-table
                  ref="tableRef"
                  tooltip-effect="dark"
                  :data="sampTable"
                  :header-cell-style="{
                    backgroundColor: '#F1F5F9',
                    color: '#666',
                    fontWeight: 700,
                  }"
                  border
                  :stripe="true"
                >
                  <el-table-column label="Sample Name" width="130">
                    <template #default="scope">
                      <el-input v-model="form.sampName"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="KO-Sample File" width="140">
                    <template #default="scope">
                      <el-upload
                        ref="upload"
                        class="upload-demo"
                        action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                        :limit="1"
                        :auto-upload="false"
                      >
                        <template #trigger>
                          <el-button type="primary">Select</el-button>
                        </template>
                      </el-upload>
                    </template>
                  </el-table-column>
                  <el-table-column width="120">
                    <el-button type="success" round size="small">+</el-button>
                    <el-button type="warning" round size="small">-</el-button>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-form-item label="Merge Tool (Optional)">
                <el-button type="primary">Merge</el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  class="w-100 filter-search mt-1"
                  icon="Promotion"
                  @click="submit"
                  >Submit
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="card w-100" style="min-height: 100%">
            <div
              v-if="!showAbundanceMap"
              id="biotaMap"
              style="width: 100%; height: 560px; background-color: #fffff5"
            ></div>
            <div
              v-if="showAbundanceMap"
              id="abundanceMap"
              style="width: 100%; height: 560px; background-color: #fffff5"
            ></div>
            <div v-if="showAbundanceMap" class="legend">
              <h4>Normalized Abundance:</h4>
              <div class="legend-item">
                <div>
                  <div class="size">0.0001%</div>
                  <div class="cricle" style="width: 7px; height: 7px"></div>
                </div>
                <div>
                  <div class="size">0.001%</div>
                  <div class="cricle" style="width: 11px; height: 11px"></div>
                </div>
                <div>
                  <div class="size">0.01%</div>
                  <div class="cricle" style="width: 17px; height: 17px"></div>
                </div>
                <div>
                  <div class="size">0.1%</div>
                  <div class="cricle" style="width: 21px; height: 21px"></div>
                </div>
                <div>
                  <div class="size">1%</div>
                  <div class="cricle" style="width: 25px; height: 25px"></div>
                </div>
                <div>
                  <div class="size">10%</div>
                  <div class="cricle" style="width: 30px; height: 30px"></div>
                </div>
                <div>
                  <div class="size">100%</div>
                  <div class="cricle" style="width: 35px; height: 35px"></div>
                </div>
              </div>
            </div>
          </div>
          <!--          <div v-if="showAbundanceMap" class="card w-100 mt-1">
            <img src="@/assets/images/biota.png" alt="" style="width: 100%" />
          </div>-->
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { nextTick, onMounted, reactive, ref } from 'vue';
  import L from 'leaflet';
  import 'leaflet/dist/leaflet.css';
  import ocean from '../../../public/geojson/ocean.json';
  import lakes from '../../../public/geojson/sample_lakes.json';
  import rivers from '../../../public/geojson/sample_rivers.json';
  import antData from '@/assets/geojson/antnest-samples.js';
  import data from '@/assets/geojson/biota_table.js';
  import dataSamples from '@/assets/geojson/biota_analysis_samples.js';

  const currentPage = ref(1);
  const pageSize = ref(10);
  const selectedTags = ref([]);
  const tableRef = ref(null);
  const table = ref(null);
  const showAbundanceMap = ref(false);

  const form = reactive({
    waterBodyName: '',
    longitudeBox: true,
    sliderLongitude: [-180.0, 180.0],
    checkLatitude: true,
    sliderLatitude: [-90, 90],
    checkLocation: true,
    location: '',
    geneInputType: '',
    speciesType: '',
    inputType: '',
    sampName: '',
  });

  const suggestions = ref([
    { value: 'Yellow Sea' },
    { value: 'Daya Bay' },
    { value: 'Tai Lake' },
    { value: 'Yellow River' },
    { value: 'Yangtze River' },
    { value: 'Bantang Hot Spring' },
    { value: '......' },
  ]);

  /** 自动补全过滤 */
  const querySearch = (queryString, cb) => {
    const results = queryString
      ? suggestions.value.filter(createFilter(queryString))
      : suggestions.value;
    cb(results);
  };
  const createFilter = queryString => {
    return suggestions => {
      return (
        suggestions.value.toLowerCase().indexOf(queryString.toLowerCase()) !==
        -1
      );
    };
  };

  const country = ref([
    { value: 'Australia' },
    { value: 'Belgium' },
    { value: 'Brazil' },
    { value: 'Bulgaria' },
    { value: 'Canada' },
    { value: 'China' },
    { value: '......' },
  ]);

  /** 自动补全过滤 */
  const queryCountrySearch = (queryString, cb) => {
    const results = queryString
      ? country.value.filter(createCountryFilter(queryString))
      : country.value;
    cb(results);
  };
  const createCountryFilter = queryString => {
    return country => {
      return (
        country.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
      );
    };
  };

  const props = {
    expandTrigger: 'hover',
  };
  const hydrosphereTypeOpt = ref([
    {
      value: 'Inland Water',
      label: 'InlandWater',
      children: [
        {
          value: 'Ice Caps & Glaciers',
          label: 'Ice Caps & Glaciers',
        },
        {
          value: 'Groundwater',
          label: 'Groundwater',
        },
        {
          value: 'Freshwater lakes',
          label: 'Freshwater lakes',
        },
        {
          value: 'Wetlands',
          label: 'Wetlands',
        },
        {
          value: 'Soil moisture',
          label: 'Soil moisture',
        },
        {
          value: 'Rivers',
          label: 'Rivers',
        },
        {
          value: 'Atmosphere',
          label: 'Atmosphere',
        },
        {
          value: 'Biota',
          label: 'Biota',
        },
        {
          value: 'Saline lakes',
          label: 'Saline lakes',
        },
        {
          value: 'Inland Seas',
          label: 'Inland Seas',
        },
      ],
    },
    {
      value: 'Oceans',
      label: 'Oceans',
      children: [
        {
          value: 'Arctic Ocean',
          label: 'Arctic Ocean',
        },
        {
          value: 'Southern Ocean',
          label: 'Southern Ocean',
        },
        {
          value: 'Baltic Sea',
          label: 'Baltic Sea',
        },
        {
          value: 'Mediterranean Sea',
          label: 'Mediterranean Sea',
        },
        {
          value: 'North Pacific Ocean',
          label: 'North Pacific Ocean',
        },
        {
          value: 'South Pacific Ocean',
          label: 'South Pacific Ocean',
        },
        {
          value: 'Indian Ocean',
          label: 'Indian Ocean',
        },
        {
          value: 'North Atlantic Ocean',
          label: 'North Atlantic Ocean',
        },
        {
          value: 'South Atlantic Ocean',
          label: 'South Atlantic Ocean',
        },
      ],
    },
  ]);

  const dataTable = reactive([...data]);
  const sampTable = reactive([{}]);

  function handleClose(tag) {
    const index = selectedTags.value.indexOf(tag);
    if (index !== -1) {
      selectedTags.value.splice(index, 1);
    }
    // 更新表格的勾选状态
    const rowIndex = dataTable.findIndex(item => item.runId === tag);
    if (rowIndex !== -1) {
      console.log('345656');
      table.value.toggleRowSelection(dataTable[rowIndex], false);
    }
  }

  function handleSelectionChange(selection) {
    const selectedTagsArray = selection.map(item => item.runId);
    selectedTags.value = selectedTagsArray;
  }

  function selectTable() {
    dataTable.forEach(item => {
      table.value.toggleRowSelection(item, true);
    });
  }

  const initMap = id => {
    var latlng = L.latLng(30, 0);

    var map = L.map(id, {
      // crs: L.CRS.Simple,
      center: latlng,
      zoom: 2,
      minZoom: 2, // 设置最小缩放级别为 10
      maxZoom: 18, // 设置最小缩放级别为 10
      // layers: [tiles],
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    L.geoJSON(ocean, {
      onEachFeature: function (feature, layer) {
        let labelLatLng;
        // 根据特征名称选择标签位置
        if (feature.properties.name === 'North Pacific Ocean') {
          labelLatLng = L.latLng(30, -150);
        } else if (feature.properties.name === 'South Pacific Ocean') {
          labelLatLng = L.latLng(-30, -140);
        } else {
          // 默认使用中心点
          labelLatLng = layer.getBounds().getCenter();
        }

        // 创建一个标记
        var label = L.marker(labelLatLng, {
          icon: L.divIcon({
            className: 'ocean-label',
            html: feature.properties.name,
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
      },

      style: function () {
        return {
          fillColor: '#1C4F80', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // var lakeLayer = null;
    L.geoJSON(lakes, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let zoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: zoom > 4 ? feature.properties.Name : '',
            }),
          );
        });
      },

      style: function () {
        return {
          fillColor: '#9ABAE7', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    L.geoJSON(rivers, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let riverZoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: riverZoom > 4 ? feature.properties.name : '',
            }),
          );
        });
      },
      style: function () {
        return {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          pane: 'riverPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    const seenProjects = new Set();
    const filteredData = antData.filter(item => {
      if (seenProjects.has(item.project_id)) {
        return false;
      }
      seenProjects.add(item.project_id);
      return true;
    });

    // 创建圆点
    if (!showAbundanceMap.value) {
      var pointsLayer = L.layerGroup().addTo(map);
      for (var i = 0; i < filteredData.length; i++) {
        let color = '';
        if (filteredData[i].type === 'Critical Zone') {
          color = '#C1232B';
        } else if (filteredData[i].type === 'Inland') {
          color = '#E87C25';
        } else {
          color = '#F2D643';
        }

        var circleMarker = L.circleMarker(
          [filteredData[i].lat, filteredData[i].lon],
          {
            radius: 3, // 点的大小
            fillColor: color,
            color: color,
            opacity: 1,
            weight: 0.5,
            className: filteredData[i].run_id,
            fillOpacity: 1, // 填充透明度设置为 1
            pane: 'pointsPane',
            renderer: canvasRenderer, // 使用 Canvas 渲染
          },
        ).addTo(pointsLayer); // 添加到图层组
      }
    } else {
      dataSamples.forEach((sample, index) => {
        if (index <= 1000) {
          const abundance = generateRandomAbundance().toFixed(5);
          const size = getSize(sample.abundance);

          const marker = L.circleMarker([sample.lat, sample.lon], {
            radius: size, // 根据丰度设置圆的大小
            fillColor: 'orange ',
            color: 'orange',
            weight: 1,
            opacity: 1,
            fillOpacity: 1,
          });

          // 创建工具提示内容
          const popupContent = `
 Number of Samples: 1<br>
            Latitude: ${sample.lat}<br>
            Longitude: ${sample.lon}<br>
             Abundance: ${sample.abundance}%<br>
             size: ${size}<br>
          `;
          // 绑定工具提示
          marker.bindTooltip(popupContent);

          marker.addTo(map);
        }
      });
    }
  };

  function generateRandomAbundance() {
    const min = 0.000001;
    const max = 0.01;
    return Math.random() * (max - min) + min; // 保留四位小数
  }

  function getSize(abundance) {
    var size = null;
    if (abundance < 0.0001 || (abundance >= 0.0001 && abundance < 0.001)) {
      size = 4;
    }
    if (abundance >= 0.001 && abundance < 0.01) {
      size = 6;
    }
    if (abundance >= 0.01 && abundance < 0.1) {
      size = 8;
    }
    if (abundance >= 0.1 && abundance < 1) {
      size = 10;
    }
    if (abundance >= 1 && abundance < 10) {
      size = 12;
    }
    if (abundance >= 10 && abundance < 100) {
      size = 14;
    }
    if (abundance >= 100) {
      size = 16;
    }
    return size;
  }

  function submit() {
    showAbundanceMap.value = true;
    nextTick(() => {
      initMap('abundanceMap');
    });
  }

  onMounted(() => {
    nextTick(() => {
      initMap('biotaMap');
      selectTable();
    });
  });

  //longitude
  const longitudeTo = ref(form.sliderLongitude[0]);
  const longitudeFrom = ref(form.sliderLongitude[1]);

  //Latitude
  const latitudeTo = ref(form.sliderLatitude[0]);
  const latitudeFrom = ref(form.sliderLatitude[1]);
</script>

<style lang="scss" scoped>
  .submit-page {
    padding: 140px 0 45px 0;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  .filter-svg {
    width: 18px;
    height: 18px;
  }

  .select-samp {
    position: absolute;
    bottom: 4px;

    .el-button:first-child {
      border-radius: 4px 0 0 4px;
    }

    .el-button:last-child {
      margin-left: 0;
      border-radius: 0 4px 4px 0;
    }
  }

  .selected-samp {
    padding: 12px 15px;
    border-radius: 4px;
    height: 172px;
    overflow-y: scroll;

    & > div {
      align-items: flex-start;
    }
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  .el-tag {
    height: 28px;
    line-height: 30px;
    padding-top: 2px;
    margin-left: 14px;
  }

  :deep(.el-tag__content) {
    //font-weight: 600;
    font-size: 14px;
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.el-upload-list) {
    margin: 0 !important;
  }

  :deep(.el-button--small.is-round) {
    width: 30px;
    height: 30px;
    font-size: 18px;
    padding: 0;
  }

  .legend {
    //position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .cricle {
    background-color: #f3a91d;
    border-radius: 50%;
  }

  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      margin-right: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  h4 {
    text-align: center;
    font-size: 1.1rem;
    margin-right: 20px;
  }

  lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }
</style>
